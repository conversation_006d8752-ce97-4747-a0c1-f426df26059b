import { httping } from "@omega/http";

/**
 * 创建资源
 * @param {Object} data - 资源数据对象
 * @param {string} data.electricityUserNumbers - 电户号（必填）
 * @param {number} data.type - 资源类型（1-发电设备，2-储能设备，3-负荷设备）（必填）
 * @param {number} data.userId - 所属用户ID（必填）
 * @param {number} data.vppId - 所属电厂ID（必填）
 * @param {string} data.resourceName - 资源名称（必填）
 * @param {number} [data.resourceType] - 资源类型(发电/储电/用电)
 * @param {number} [data.registeredCapacity] - 报装容量
 * @param {number} [data.responseMode] - 响应方式(自动/手动)
 * @param {boolean} [data.platformDirectControl] - 平台直控
 * @param {number} [data.maximumOperablePower] - 最大可运行功率
 * @param {number} [data.minmumOperablePower] - 最小可运行功率
 * @param {number} [data.maximumUpscalingRate] - 最大上调速率
 * @param {number} [data.maximumDownwardRate] - 最大下调速率
 * @param {number} [data.longitude] - 经度
 * @param {number} [data.latitude] - 纬度
 * @param {string} [data.contactPerson] - 联系人
 * @param {string} [data.phoneNumber] - 联系电话
 * @param {number} [data.district] - 区域
 * @param {string} [data.address] - 地址
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: VppResourceBO, // 资源详细信息
 *   msg: string,
 *   total: number
 * }
 */
export function createResource(data) {
  return httping({
    url: `/vpp/api/v1/resource-manager/resource-config`,
    method: "POST",
    data
  });
}

/**
 * 检查资源编码是否存在
 * @param {string} code - 资源编码
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: boolean, // true表示编码已存在，false表示不存在
 *   msg: string,
 *   total: number
 * }
 */
export function checkResourceCodeExists(code) {
  return httping({
    url: `/vpp/api/v1/resource-manager/resource-config/check-code/${code}`,
    method: "GET"
  });
}

/**
 * 根据编码获取资源
 * @param {string} code - 资源编码
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: VppResourceBO, // 资源详细信息
 *   msg: string,
 *   total: number
 * }
 */
export function getResourceByCode(code) {
  return httping({
    url: `/vpp/api/v1/resource-manager/resource-config/code/${code}`,
    method: "GET"
  });
}

/**
 * 统计资源数量
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: number, // 资源总数
 *   msg: string,
 *   total: number
 * }
 */
export function countResources() {
  return httping({
    url: `/vpp/api/v1/resource-manager/resource-config/count`,
    method: "GET"
  });
}

/**
 * 分页查询资源列表
 * @param {Object} data - 查询参数
 * @param {number} data.pageNum - 页码（从1开始）
 * @param {number} data.pageSize - 每页数量
 * @param {string} [data.name] - 资源名称（可选）
 * @param {string} [data.code] - 资源编码（可选）
 * @param {number} [data.type] - 资源类型（可选）
 * @param {number} [data.status] - 状态（可选）
 * @param {number} [data.vppId] - 所属VPP ID（可选）
 * @param {number} [data.siteId] - 所属站点ID（可选）
 * @param {string} [data.location] - 地理位置（可选）
 * @param {string} [data.manufacturer] - 制造商（可选）
 * @param {string} [data.orderBy] - 排序字段（可选）
 * @param {string} [data.orderDirection] - 排序方向（可选）
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: {
 *     records: Array<VppResourceBO>, // 资源列表
 *     total: number, // 总记录数
 *     pageNum: number, // 当前页码
 *     pageSize: number, // 每页数量
 *     pages: number, // 总页数
 *     hasNext: boolean, // 是否有下一页
 *     hasPrevious: boolean // 是否有上一页
 *   },
 *   msg: string,
 *   total: number
 * }
 */
export function getResourcePage(data) {
  return httping({
    url: `/vpp/api/v1/resource-manager/resource-config/page`,
    method: "POST",
    data
  });
}

/**
 * 根据ID查询资源
 * @param {number} id - 资源ID
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: VppResourceBO, // 资源详细信息
 *   msg: string,
 *   total: number
 * }
 */
export function getResourceById(id) {
  return httping({
    url: `/vpp/api/v1/resource-manager/resource-config/${id}`,
    method: "GET"
  });
}

/**
 * 更新资源
 * @param {number} id - 资源ID
 * @param {Object} data - 资源数据对象（同创建资源的参数）
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: VppResourceBO, // 更新后的资源信息
 *   msg: string,
 *   total: number
 * }
 */
export function updateResource(id, data) {
  return httping({
    url: `/vpp/api/v1/resource-manager/resource-config/${id}`,
    method: "PUT",
    data
  });
}

/**
 * 删除资源
 * @RequestBody {array} ids - 资源ID
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: boolean, // true表示删除成功
 *   msg: string,
 *   total: number
 * }
 */
export function deleteResource(ids) {
  return httping({
    url: `/vpp/api/v1/resource-manager/resource-config/batch`,
    method: "DELETE",
    data: ids
  });
}

/**
 * 获取资源下站点列表
 * @param {number} resourceId - 资源ID
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: Array<VppSiteBO>, // 站点列表
 *   msg: string,
 *   total: number
 * }
 */
export function getSitesByResource(resourceId) {
  return httping({
    url: `/vpp/api/v1/resource-manager/resource-config/${resourceId}/sites`,
    method: "GET"
  });
}
