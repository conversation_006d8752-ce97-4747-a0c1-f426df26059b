import { httping } from "@omega/http";

/**
 * 虚拟电厂配置管理 API
 * 基础路径: /vpp/api/v1/resource-manager/vpp-config
 */

/**
 * 创建虚拟电厂
 * @param {Object} data - 虚拟电厂数据对象
 * @param {string} data.name - VPP名称，最大100字符
 * @param {string} data.code - VPP编码，最大50字符，只能包含大写字母、数字和下划线
 * @param {number} data.type - VPP类型（1-聚合型，2-协调型，3-混合型）
 * @param {number} [data.totalCapacity] - 总装机容量（MW），0-10000
 * @param {number} [data.availableCapacity] - 可用容量（MW），0-10000
 * @param {number} data.status - 状态（1-正常，2-维护，3-故障）
 * @param {string} [data.location] - 地理位置，最大200字符
 * @param {string} [data.manager] - 负责人，最大50字符
 * @param {string} [data.phone] - 联系电话，手机号格式
 * @param {string} [data.email] - 邮箱，邮箱格式
 * @param {string} [data.description] - 描述信息，最大500字符
 * @returns {Promise<Object>} 创建结果
 */
export function createVpp(data) {
    return httping({
        url: "/vpp/api/v1/resource-manager/vpp-config",
        method: "POST",
        data
    });
}

/**
 * 根据ID查询虚拟电厂
 * @param {number} id - 虚拟电厂ID
 * @returns {Promise<Object>} 虚拟电厂详细信息
 */
export function getVppById(id) {
    return httping({
        url: `/vpp/api/v1/resource-manager/vpp-config/${id}`,
        method: "GET"
    });
}

/**
 * 更新虚拟电厂
 * @param {number} id - 虚拟电厂ID
 * @param {Object} data - 更新数据对象，字段同创建接口，所有字段都是可选的
 * @returns {Promise<Object>} 更新结果
 */
export function updateVpp(id, data) {
    return httping({
        url: `/vpp/api/v1/resource-manager/vpp-config/${id}`,
        method: "PUT",
        data
    });
}

/**
 * 删除虚拟电厂
 * @param {number} id - 虚拟电厂ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteVpp(id) {
    return httping({
        url: `/vpp/api/v1/resource-manager/vpp-config/${id}`,
        method: "DELETE"
    });
}

/**
 * 分页查询虚拟电厂列表
 * @param {Object} data - 查询参数
 * @param {number} [data.pageNum=1] - 当前页码，默认1，最小值1
 * @param {number} [data.pageSize=10] - 每页大小，默认10，范围1-100
 * @param {string} [data.name] - VPP名称（模糊查询）
 * @param {string} [data.code] - VPP编码（精确查询）
 * @param {number} [data.type] - VPP类型（1-聚合型，2-协调型，3-混合型）
 * @param {number} [data.status] - 状态（1-正常，2-维护，3-故障）
 * @param {string} [data.location] - 地理位置（模糊查询）
 * @param {string} [data.manager] - 负责人（模糊查询）
 * @param {string} [data.sortField] - 排序字段
 * @param {string} [data.sortOrder="desc"] - 排序方向（asc-升序，desc-降序），默认desc
 * @returns {Promise<Object>} 分页查询结果
 */
export function getVppPage(data = {}) {
    return httping({
        url: "/vpp/api/v1/resource-manager/vpp-config/page",
        method: "POST",
        data: data
    });
}

/**
 * 根据类型查询虚拟电厂列表
 * @param {number} type - 虚拟电厂类型（1-聚合型，2-协调型，3-混合型）
 * @returns {Promise<Object>} 虚拟电厂列表
 */
export function getVppByType(type) {
    return httping({
        url: `/vpp/api/v1/resource-manager/vpp-config/type/${type}`,
        method: "GET"
    });
}

/**
 * 检查虚拟电厂编码是否存在
 * @param {string} code - 虚拟电厂编码
 * @returns {Promise<Object>} 检查结果，data为true表示编码已存在，false表示编码不存在
 */
export function checkVppCode(code) {
    return httping({
        url: `/vpp/api/v1/resource-manager/vpp-config/check-code/${code}`,
        method: "GET"
    });
}
