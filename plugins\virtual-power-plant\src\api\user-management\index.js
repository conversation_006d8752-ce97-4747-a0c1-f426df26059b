import { httping } from "@omega/http";

/**
 * 创建用户
 * @param {Object} data - 用户数据对象
 * @param {string} data.userName - 用户名称
 * @param {string} data.contactPerson - 联系人
 * @param {string} data.phoneNumber - 联系电话
 * @param {string} data.address - 地址
 * @param {number} data.vppId - 虚拟电厂ID
 * @param {number} data.region - 所属区域
 * @param {number} data.city - 所属市
 * @param {number} data.district - 所属区
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: {
 *     id: number, // 用户ID
 *     user_name: string, // 用户名称
 *     contact_person: string, // 联系人
 *     phone_number: number, // 联系电话
 *     address: string, // 地址
 *     vpp_id: number, // 虚拟电厂ID
 *     region: number, // 所属区域
 *     city: number, // 所属市
 *     district: number, // 所属区
 *     resource_count: number, // 资源数量
 *     create_time: number, // 创建时间
 *     update_time: number // 更新时间
 *   },
 *   msg: string,
 *   total: number
 * }
 */
export function createUser(data) {
  return httping({
    url: `/vpp/api/v1/resource-manager/user/create`,
    method: "POST",
    data
  });
}

/**
 * 检查用户名是否存在
 * @param {string} username - 用户名
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: boolean, // true表示用户名已存在，false表示不存在
 *   msg: string,
 *   total: number
 * }
 */
export function checkUsernameExists(username) {
  return httping({
    url: `/vpp/api/v1/resource-manager/user/check-username/${username}`,
    method: "GET"
  });
}

/**
 * 分页查询用户列表
 * @param {Object} data - 查询参数
 * @param {number} data.index - 页码（从0开始）
 * @param {number} data.limit - 每页数量
 * @param {string} [data.userName] - 用户名称（可选）
 * @param {string} [data.contactPerson] - 联系人（可选）
 * @param {string} [data.phoneNumber] - 联系电话（可选）
 * @param {string} [data.address] - 地址（可选）
 * @param {number} [data.vppId] - 虚拟电厂ID（可选）
 * @param {number} [data.region] - 所属区域（可选）
 * @param {number} [data.city] - 所属市（可选）
 * @param {number} [data.district] - 所属区（可选）
 * @param {number} [data.userId] - 用户ID（可选，用于查询特定用户）
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: {
 *     records: Array<VppUserBO>, // 用户列表
 *     total: number, // 总记录数
 *     pageNum: number, // 当前页码
 *     pageSize: number, // 每页数量
 *     pages: number, // 总页数
 *     hasNext: boolean, // 是否有下一页
 *     hasPrevious: boolean // 是否有上一页
 *   },
 *   msg: string,
 *   total: number
 * }
 */
export function getUserPage(data) {
  return httping({
    url: `/vpp/api/v1/resource-manager/user/page`,
    method: "POST",
    data
  });
}

/**
 * 更新用户
 * @param {number} id - 用户ID
 * @param {Object} data - 用户数据对象
 * @param {string} data.userName - 用户名称
 * @param {string} data.contactPerson - 联系人
 * @param {string} data.phoneNumber - 联系电话
 * @param {string} data.address - 地址
 * @param {number} data.vppId - 虚拟电厂ID
 * @param {number} data.region - 所属区域
 * @param {number} data.city - 所属市
 * @param {number} data.district - 所属区
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: VppUserBO, // 更新后的用户信息
 *   msg: string,
 *   total: number
 * }
 */
export function updateUser(id, data) {
  return httping({
    url: `/vpp/api/v1/resource-manager/user/${id}`,
    method: "PUT",
    data
  });
}

/**
 * 删除用户
 * @param {Object} deleteNode - 删除对象
 * @returns {Promise} 返回Promise对象
 * 入参对象
 * {
 *   parentId：number  虚拟电厂id,
 *   ids:array 用户id集合
 * }
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: boolean, // true表示删除成功
 *   msg: string,
 *   total: number
 * }
 */
export function deleteUser(deleteNode) {
  return httping({
    url: `/vpp/api/v1/resource-manager/user/delete-by-ids`,
    method: "DELETE",
    data: deleteNode
  });
}

/**
 * 获取用户管理的资源列表
 * @param {number} id - 用户ID
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: Array<VppResourceBO>, // 资源列表
 *   msg: string,
 *   total: number
 * }
 */
export function getUserResources(id) {
  return httping({
    url: `/vpp/api/v1/resource-manager/user/${id}/resources`,
    method: "GET"
  });
}
