<template>
  <div class="debug-test bg-BG p-J3">
    <div class="test-header bg-BG1 rounded-lg p-J3 mb-J3">
      <h2 class="text-T1 font-medium mb-J2">{{ $T("调试测试页面") }}</h2>
      <p class="text-T2 mb-J3">
        {{ $T("测试新增站点按钮功能") }}
      </p>
    </div>

    <!-- 模拟选择资源 -->
    <div class="resource-selection bg-BG1 rounded-lg p-J3 mb-J3">
      <h3 class="text-T1 font-medium mb-J2">{{ $T("模拟选择资源") }}</h3>
      <div class="resource-buttons flex gap-J2 mb-J3">
        <el-button
          v-for="resource in mockResources"
          :key="resource.tree_id"
          :type="
            selectedResource?.tree_id === resource.tree_id
              ? 'primary'
              : 'default'
          "
          @click="selectResource(resource)"
        >
          {{ resource.name }} ({{ resource.resourceType }})
        </el-button>
      </div>

      <div v-if="selectedResource" class="selected-info bg-BG2 rounded p-J2">
        <div class="text-T2">
          <span class="font-medium">{{ $T("当前选中") }}:</span>
          {{ selectedResource.name }} - {{ $T("资源类型") }}:
          {{ selectedResource.resourceType }}
        </div>

        <!-- 测试详情面板按钮 -->
        <div class="mt-J2">
          <h4 class="text-T1 text-sm mb-J1">{{ $T("测试详情面板") }}:</h4>
          <div class="flex gap-J1">
            <el-button @click="testStorageDetail" type="primary" size="mini">
              {{ $T("储能类详情") }}
            </el-button>
            <el-button @click="testRenewableDetail" type="success" size="mini">
              {{ $T("新能源类详情") }}
            </el-button>
            <el-button @click="testOtherDetail" type="warning" size="mini">
              {{ $T("其他类详情") }}
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 站点管理组件 -->
    <div class="site-management bg-BG1 rounded-lg p-J3">
      <h3 class="text-T1 font-medium mb-J2">{{ $T("站点管理") }}</h3>
      <SiteManagement :node="selectedResource" />
    </div>

    <!-- 调试信息 -->
    <div class="debug-info bg-BG1 rounded-lg p-J3 mt-J3">
      <h3 class="text-T1 font-medium mb-J2">{{ $T("调试信息") }}</h3>
      <div class="debug-content bg-BG2 rounded p-J2">
        <pre class="text-T2 text-sm">{{ debugInfo }}</pre>
      </div>
    </div>

    <!-- 站点详情面板 -->
    <SiteDetailPanel
      :visible="showDetailPanel"
      :site-data="currentSiteDetail"
      @close="showDetailPanel = false"
    />
  </div>
</template>

<script>
import SiteManagement from "./SiteManagement.vue";
import SiteDetailPanel from "./SiteDetailPanel.vue";

export default {
  name: "DebugTest",
  components: {
    SiteManagement,
    SiteDetailPanel
  },
  data() {
    return {
      selectedResource: null,
      // 详情面板相关
      showDetailPanel: false,
      currentSiteDetail: null,
      // mockResources已移除，使用API数据
      mockResources: []
    };
  },
  computed: {
    debugInfo() {
      return {
        selectedResource: this.selectedResource,
        timestamp: new Date().toLocaleTimeString()
      };
    }
  },
  methods: {
    selectResource(resource) {
      this.selectedResource = resource;
      console.log("🎯 选中资源:", resource);
    },

    // 测试储能类详情
    testStorageDetail() {
      this.currentSiteDetail = {
        id: 1,
        site_name: "用户侧储能站-测试",
        site_type: 2, // 用户侧储能
        site_address: "北京市朝阳区储能测试地址123号",
        contact_person: "张三",
        phone_number: "13800138001",
        longitude: "116.4074",
        latitude: "39.9042",
        voltage_level: "10kV",
        grid_voltage: 10,
        total_capacity: 1000,
        total_storage: 800,
        operation_date: "2024-01-15",
        related_room: "1号储能机房",
        imageUrl: null
      };
      this.showDetailPanel = true;
    },

    // 测试新能源类详情
    testRenewableDetail() {
      this.currentSiteDetail = {
        id: 2,
        site_name: "分布式光伏站-测试",
        site_type: 8, // 分布式光伏
        site_address: "上海市浦东新区光伏测试地址456号",
        contact_person: "李四",
        phone_number: "13800138002",
        longitude: "121.4737",
        latitude: "31.2304",
        voltage_level: "35kV",
        grid_voltage: 35,
        total_capacity: 2000,
        generation_mode: "self_use",
        operation_date: "2024-02-20",
        related_room: "光伏控制室",
        imageUrl: null
      };
      this.showDetailPanel = true;
    },

    // 测试其他类详情
    testOtherDetail() {
      this.currentSiteDetail = {
        id: 3,
        site_name: "充电站-测试",
        site_type: 4, // 充电站
        site_address: "深圳市南山区充电站测试地址789号",
        contact_person: "王五",
        phone_number: "13800138003",
        longitude: "114.0579",
        latitude: "22.5431",
        voltage_level: "0.4kV",
        operation_date: "2024-03-10",
        related_room: "充电控制室",
        imageUrl: null
      };
      this.showDetailPanel = true;
    }
  }
};
</script>

<style scoped>
.debug-test {
  min-height: 100vh;
}

.resource-buttons {
  flex-wrap: wrap;
}

.debug-content pre {
  white-space: pre-wrap;
  word-break: break-all;
  font-family: "Courier New", monospace;
  font-size: 12px;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .resource-buttons {
    flex-direction: column;
  }

  .resource-buttons .el-button {
    width: 100%;
    margin-bottom: 8px;
  }
}
</style>
