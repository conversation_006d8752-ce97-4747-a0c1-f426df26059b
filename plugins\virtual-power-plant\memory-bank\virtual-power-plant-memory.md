# 虚拟电厂资源配置插件记忆文档

## 1. 项目概述

### 1.1 项目目标

本项目旨在构建一个可扩展的虚拟电厂（VPP）资源管理平台。它采用多插件架构，允许独立开发、部署和维护各项业务功能，从而提高开发效率和系统的可维护性。

### 1.2 项目背景

随着能源行业的数字化转型，对分布式能源（如太阳能、储能、电动汽车等）进行统一管理和优化调度的需求日益增长。现有系统往往缺乏灵活性和可扩展性，难以适应快速变化的业务需求。因此，本项目旨在提供一个统一的、插件化的平台来解决这些问题。

## 2. 技术栈与架构

### 2.1 核心技术栈

- **前端**: Vue.js 2.7.8, Vue Router 3.x, Vuex 3.x
- **UI 框架**: Element UI 2.15.6, Tailwind CSS 3.x
- **样式**: SCSS, CSS 变量
- **微前端**: `@omega/app`, `@altair/lord`, `@altair/blade`
- **业务组件**: `cet-chart`, `cet-common`, `@omega/widget`, `@omega/trend`, `@omega/dashboard`
- **工具库**: Lodash, Moment.js, Crypto-js, jQuery
- **构建工具**: Vue CLI 5.x, Webpack 5.x, Babel 7.x
- **包管理**: pnpm, pnpm workspace

### 2.2 系统架构

系统采用微前端架构，其中：

- **主应用 (`@omega/app`)**: 负责插件的注册、生命周期管理和全局配置。
- **业务插件**: 位于 `plugins/` 目录下，作为独立的单元进行开发和部署。`vpp-resource-manager` 是其中一个核心插件。
- **插件注册**: 插件通过 `omegaApp.plugin.register` 在 `src/omega/index.js` 文件中注册其功能。

## 3. 数据模型

项目定义了清晰的层级数据模型，从虚拟电厂到设备和数据点：

- **VPP (虚拟电厂)**
- **USER (用户)**
- **RESOURCE (资源)**
- **SITE (站点)**
- **DEVICE (设备)**
- **DATA_POINT (数据点)**

详细的实体关系和字段定义请参考 `doc/虚拟电厂平台资源管理模块设计文档.md`。

## 4. API 接口

### 4.1 规范

- **风格**: RESTful
- **基础路径**: `/vpp/api/v1/resource-manager/`
- **命名**: 资源使用名词复数形式
- **响应格式**: 统一的 JSON 结构，包含 `code`, `message`, `data`, `timestamp`。

### 4.2 核心接口

项目提供了一整套用于管理 VPP 资源的 CRUD 接口，包括：

- 虚拟电厂管理
- 用户管理
- 资源管理
- 站点管理
- 设备管理
- VPP 层级树缓存管理

**注意**: 部分接口已经过调整和优化，例如，用户查询已统一为 `getUserPage` 接口。详细信息请参阅 `doc/` 目录下的 API 相关文档。

## 5. 前端设计

### 5.1 页面结构

资源管理模块主要由以下页面组成：

- 虚拟电厂管理
- 用户管理
- 资源配置
- 站点管理
- 设备管理

### 5.2 组件设计

- **核心业务组件**:
  - `VppTree.vue`: 用于展示和操作 VPP 层级树。
  - `VppManagement.vue`, `UserManagement.vue`, `ResourceManagement.vue`, `SiteManagement.vue`, `DeviceManagement.vue`: 分别对应各项管理功能。
- **基础组件**: 广泛使用 `cet-` 系列组件（如 `CetTable`, `CetButton`, `CetSimpleSelect`）以保证 UI 一致性和开发效率。

### 5.3 状态管理

- **Vuex**: 用于统一管理应用状态。
- **模块化**: `vppResource.js` 模块专门负责管理虚拟电厂资源相关的状态，包括数据、加载状态和错误信息。

## 6. 开发规范

项目有非常详细的开发规范，涵盖了项目结构、命名、编码风格、API 设计、状态管理、国际化、组件开发、测试和代码提交等各个方面。所有开发者都应严格遵守这些规范。

详情请参阅 `memory-bank/developmentStandards.md`。

## 7. 当前状态与下一步计划

### 7.1 已完成

- `vpp-resource-manager` 插件的整体架构重构已完成，包括 API 层、状态管理层和组件架构。
- 目录结构已经规范化。
- 基础的 `cet` 组件体系已经集成。

### 7.2 进行中

- 完善各个管理组件的具体业务逻辑，如增删改查的表单实现。

### 7.3 下一步

- 实现数据关联配置功能（例如，站点与房间的关联）。
- 对接真实的后端 API，并完善数据交互和错误处理。
- 优化性能和用户体验。

## 8. 关键信息源

- **设计文档**: `plugins/virtual-power-plant/doc/`
- **项目上下文与规范**: `plugins/virtual-power-plant/memory-bank/`
- **当前活动与进展**: `plugins/virtual-power-plant/memory-bank/activeContext.md` 和 `plugins/virtual-power-plant/memory-bank/progress.md`
