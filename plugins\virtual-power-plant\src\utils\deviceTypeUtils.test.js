/**
 * 设备类型工具函数测试
 * 用于验证设备类型映射和处理逻辑是否正确
 */

import { 
  getDeviceTypeFromModel, 
  processDeviceData, 
  MODEL_LABEL_MAP,
  isValidDeviceType 
} from './deviceTypeUtils.js';

// 测试数据
const testDevices = [
  // 测试 modelLabel 映射
  {
    name: "逆变器设备1",
    modelLabel: "pv_inverter",
    id: "inv_001",
    expected: "光伏逆变器"
  },
  {
    name: "电池设备1", 
    modelLabel: "battery",
    id: "bat_001",
    expected: "电池设备"
  },
  {
    name: "PCS设备1",
    modelLabel: "pcs", 
    id: "pcs_001",
    expected: "PCS设备"
  },
  {
    name: "储能容器1",
    modelLabel: "pv_energycontainer",
    id: "ec_001", 
    expected: "储能容器"
  },
  {
    name: "气象仪1",
    modelLabel: "pv_meteorograph",
    id: "met_001",
    expected: "气象仪"
  },
  {
    name: "充电桩1",
    modelLabel: "pv_chargingstation", 
    id: "cs_001",
    expected: "充电桩"
  },
  {
    name: "DC汇流箱1",
    modelLabel: "pv_dccombinerbox",
    id: "dcb_001",
    expected: "DC汇流箱"
  },
  
  // 测试 model 字段映射（当没有 modelLabel 时）
  {
    name: "逆变器设备2",
    pv_inverter_model: "INV-2000",
    id: "inv_002",
    expected: "光伏逆变器"
  },
  {
    name: "电池设备2",
    battery_model: "BAT-5000", 
    id: "bat_002",
    expected: "电池设备"
  },
  
  // 测试未知设备
  {
    name: "未知设备1",
    id: "unk_001",
    expected: "未知设备"
  },
  
  // 测试新的 modelLabel（不在映射表中）
  {
    name: "新设备类型",
    modelLabel: "new_device_type",
    id: "new_001", 
    expected: "new_device_type" // 应该返回原始值
  }
];

/**
 * 运行设备类型映射测试
 */
function runDeviceTypeTests() {
  console.log('🧪 开始设备类型映射测试...\n');
  
  let passedTests = 0;
  let totalTests = testDevices.length;
  
  testDevices.forEach((testDevice, index) => {
    const { expected, ...device } = testDevice;
    const result = getDeviceTypeFromModel(device);
    const passed = result === expected;
    
    console.log(`📋 测试 ${index + 1}: ${device.name}`);
    console.log(`   输入: modelLabel="${device.modelLabel || 'undefined'}", model字段="${Object.keys(device).filter(k => k.endsWith('_model')).join(', ') || 'none'}"`);
    console.log(`   期望: "${expected}"`);
    console.log(`   实际: "${result}"`);
    console.log(`   结果: ${passed ? '✅ 通过' : '❌ 失败'}\n`);
    
    if (passed) {
      passedTests++;
    }
  });
  
  console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`);
  console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%\n`);
  
  return passedTests === totalTests;
}

/**
 * 测试 processDeviceData 函数
 */
function testProcessDeviceData() {
  console.log('🔧 测试 processDeviceData 函数...\n');
  
  const testDevice = {
    id: "test_001",
    name: "测试逆变器",
    modelLabel: "pv_inverter"
  };
  
  const processed = processDeviceData(testDevice, "pv_inverter");
  
  console.log('输入设备数据:', testDevice);
  console.log('处理后数据:', processed);
  
  const expectedFields = ['id', 'name', 'modelLabel', 'deviceName', 'deviceType'];
  const hasAllFields = expectedFields.every(field => processed.hasOwnProperty(field));
  const correctDeviceType = processed.deviceType === "光伏逆变器";
  const correctDeviceName = processed.deviceName === "测试逆变器";
  
  console.log(`✅ 包含所有必需字段: ${hasAllFields}`);
  console.log(`✅ 设备类型正确: ${correctDeviceType}`);
  console.log(`✅ 设备名称正确: ${correctDeviceName}\n`);
  
  return hasAllFields && correctDeviceType && correctDeviceName;
}

/**
 * 测试设备类型验证函数
 */
function testDeviceTypeValidation() {
  console.log('✅ 测试设备类型验证函数...\n');
  
  const validTypes = ["电池设备", "PCS设备", "光伏逆变器", "未知设备"];
  const invalidTypes = ["不存在的设备", ""];
  
  let allValid = true;
  
  validTypes.forEach(type => {
    const isValid = isValidDeviceType(type);
    console.log(`"${type}": ${isValid ? '✅ 有效' : '❌ 无效'}`);
    if (!isValid) allValid = false;
  });
  
  invalidTypes.forEach(type => {
    const isValid = isValidDeviceType(type);
    console.log(`"${type}": ${isValid ? '❌ 应该无效但返回有效' : '✅ 正确识别为无效'}`);
    if (isValid) allValid = false;
  });
  
  console.log(`\n验证函数测试: ${allValid ? '✅ 通过' : '❌ 失败'}\n`);
  
  return allValid;
}

/**
 * 显示当前支持的所有设备类型
 */
function showSupportedDeviceTypes() {
  console.log('📋 当前支持的设备类型映射:\n');
  
  Object.entries(MODEL_LABEL_MAP).forEach(([key, value]) => {
    console.log(`  ${key} → ${value}`);
  });
  
  console.log(`\n总计支持 ${Object.keys(MODEL_LABEL_MAP).length} 种设备类型\n`);
}

// 运行所有测试
export function runAllTests() {
  console.log('🚀 设备类型工具函数完整测试\n');
  console.log('='.repeat(50) + '\n');
  
  showSupportedDeviceTypes();
  
  const test1 = runDeviceTypeTests();
  const test2 = testProcessDeviceData();
  const test3 = testDeviceTypeValidation();
  
  console.log('='.repeat(50));
  console.log(`🎯 总体测试结果: ${test1 && test2 && test3 ? '✅ 全部通过' : '❌ 存在失败'}`);
  console.log('='.repeat(50));
  
  return test1 && test2 && test3;
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.runDeviceTypeTests = runAllTests;
} else if (typeof module !== 'undefined' && module.exports) {
  // Node.js 环境
  runAllTests();
}
