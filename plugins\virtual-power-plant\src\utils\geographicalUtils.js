/**
 * 根据省市区代码获取名称，使用/拼接层级
 * @param {Object} geographicalData - 地理位置数据
 * @param {string} provinceCode - 省份代码
 * @param {string} cityCode - 城市代码（可选）
 * @param {string} districtCode - 区县代码（可选）
 * @returns {string} 返回用/拼接的地理位置名称
 */
export function getGeographicalNameByCode(geographicalData, provinceCode, cityCode = null, districtCode = null) {
  if (!geographicalData || !provinceCode) {
    return '--';
  }
  const names = [];
  // 查找省份
  const province = geographicalData.find(item => item.code === provinceCode);
  if (!province) {
    return '';
  }
  names.push(province.name);

  // 查找城市
  if (cityCode && province.children) {
    const city = province.children.find(item => item.code === cityCode);
    if (city) {
      names.push(city.name);

      // 查找区县
      if (districtCode && city.children) {
        const district = city.children.find(item => item.code === districtCode);
        if (district) {
          names.push(district.name);
        }
      }
    }
  }

  return names.join('/');
}