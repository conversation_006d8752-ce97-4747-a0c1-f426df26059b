# 虚拟电厂管理接口文档

## 概述

本文档描述了虚拟电厂管理模块的REST API接口，包括虚拟电厂的创建、查询、更新、删除等操作。

**基础路径**: `/vpp/api/v1/resource-manager/vpp-config`

**Content-Type**: `application/json`

## 通用响应格式

所有接口都使用统一的响应格式：

```json
{
  "code": 0,           // 响应码，0表示成功，非0表示失败
  "msg": "执行成功",    // 响应消息
  "data": {},          // 响应数据
  "total": 0           // 总数（分页查询时使用）
}
```

## 分页响应格式

分页查询接口的data字段结构：

```json
{
  "records": [],       // 数据列表
  "total": 100,        // 总记录数
  "pageNum": 1,        // 当前页码
  "pageSize": 10,      // 每页大小
  "pages": 10,         // 总页数
  "hasPrevious": false, // 是否有上一页
  "hasNext": true      // 是否有下一页
}
```

## 接口列表

### 1. 创建虚拟电厂

**接口地址**: `POST /vpp/api/v1/resource-manager/vpp-config`

**接口描述**: 创建新的虚拟电厂

**请求参数**:

```json
{
  "name": "示例虚拟电厂",                    // 必填，VPP名称，最大100字符
  "code": "VPP_EXAMPLE_001",              // 必填，VPP编码，最大50字符，只能包含大写字母、数字和下划线
  "type": 1,                             // 必填，VPP类型（1-聚合型，2-协调型，3-混合型）
  "totalCapacity": 1000.5,               // 可选，总装机容量（MW），0-10000
  "availableCapacity": 800.0,            // 可选，可用容量（MW），0-10000
  "status": 1,                           // 必填，状态（1-正常，2-维护，3-故障）
  "location": "北京市朝阳区",               // 可选，地理位置，最大200字符
  "manager": "张三",                      // 可选，负责人，最大50字符
  "phone": "13800138000",                // 可选，联系电话，手机号格式
  "email": "<EMAIL>",          // 可选，邮箱，邮箱格式
  "description": "这是一个示例虚拟电厂"      // 可选，描述信息，最大500字符
}
```

**响应示例**:

```json
{
  "code": 0,
  "msg": "执行成功",
  "data": {
    "id": 1,
    "vpp_name": "示例虚拟电厂",
    "vpp_code": "VPP_EXAMPLE_001",
    "province": 110000,
    "vpp_type": "聚合型",
    "description": "这是一个示例虚拟电厂",
    "user_count": 0,
    "resource_count": 0,
    "adjustable_capacity": 800.0,
    "picture": null,
    "createtime": 1640995200000,
    "operatorcode": "OP001",
    "create_time": 1640995200000,
    "update_time": 1640995200000,
    "vpp_user_list": [],
    "children": []
  }
}
```

### 2. 根据ID查询虚拟电厂

**接口地址**: `GET /vpp/api/v1/resource-manager/vpp-config/{id}`

**接口描述**: 根据虚拟电厂ID查询详细信息

**路径参数**:
- `id`: 虚拟电厂ID（必填，Long类型）

**响应示例**:

```json
{
  "code": 0,
  "msg": "执行成功",
  "data": {
    "id": 1,
    "vpp_name": "示例虚拟电厂",
    "vpp_code": "VPP_EXAMPLE_001",
    "province": 110000,
    "vpp_type": "聚合型",
    "description": "这是一个示例虚拟电厂",
    "user_count": 5,
    "resource_count": 10,
    "adjustable_capacity": 800.0,
    "picture": "http://example.com/image.jpg",
    "createtime": 1640995200000,
    "operatorcode": "OP001",
    "create_time": 1640995200000,
    "update_time": 1640995200000,
    "vpp_user_list": [
      {
        "id": 1,
        "userName": "用户1",
        "vppId": 1,
        "phoneNumber": "13800138001",
        "contactPerson": "联系人1",
        "address": "北京市朝阳区",
        "resourceCount": 2,
        "city": 110100,
        "province": 110000,
        "district": 110101,
        "createTime": 1640995200000,
        "updateTime": 1640995200000,
        "vpp_resource_list": [],
        "children": []
      }
    ],
    "children": []
  }
}
```

### 3. 更新虚拟电厂

**接口地址**: `PUT /vpp/api/v1/resource-manager/vpp-config/{id}`

**接口描述**: 更新虚拟电厂信息

**路径参数**:
- `id`: 虚拟电厂ID（必填，Long类型）

**请求参数**: 同创建接口，所有字段都是可选的，只更新传入的字段

**响应示例**: 同查询接口响应格式

### 4. 删除虚拟电厂

**接口地址**: `DELETE /vpp/api/v1/resource-manager/vpp-config/{id}`

**接口描述**: 删除虚拟电厂

**路径参数**:
- `id`: 虚拟电厂ID（必填，Long类型）

**响应示例**:

```json
{
  "code": 0,
  "msg": "执行成功",
  "data": true
}
```

### 5. 分页查询虚拟电厂列表

**接口地址**: `POST /vpp/api/v1/resource-manager/vpp-config/page`

**接口描述**: 根据条件分页查询虚拟电厂列表

**请求参数**:

```json
{
  "pageNum": 1,                    // 可选，当前页码，默认1，最小值1
  "pageSize": 10,                  // 可选，每页大小，默认10，范围1-100
  "name": "示例",                   // 可选，VPP名称（模糊查询）
  "code": "VPP_001",               // 可选，VPP编码（精确查询）
  "type": 1,                       // 可选，VPP类型（1-聚合型，2-协调型，3-混合型）
  "status": 1,                     // 可选，状态（1-正常，2-维护，3-故障）
  "location": "北京",               // 可选，地理位置（模糊查询）
  "manager": "张",                  // 可选，负责人（模糊查询）
  "sortField": "createTime",       // 可选，排序字段
  "sortOrder": "desc"              // 可选，排序方向（asc-升序，desc-降序），默认desc
}
```

**响应示例**:

```json
{
  "code": 0,
  "msg": "执行成功",
  "data": {
    "records": [
      {
        "id": 1,
        "vpp_name": "示例虚拟电厂1",
        "vpp_code": "VPP_EXAMPLE_001",
        "province": 110000,
        "vpp_type": "聚合型",
        "description": "这是一个示例虚拟电厂",
        "user_count": 5,
        "resource_count": 10,
        "adjustable_capacity": 800.0,
        "picture": null,
        "createtime": 1640995200000,
        "operatorcode": "OP001",
        "create_time": 1640995200000,
        "update_time": 1640995200000,
        "vpp_user_list": [],
        "children": []
      }
    ],
    "total": 1,
    "pageNum": 1,
    "pageSize": 10,
    "pages": 1,
    "hasPrevious": false,
    "hasNext": false
  }
}
```

### 6. 根据类型查询虚拟电厂列表

**接口地址**: `GET /vpp/api/v1/resource-manager/vpp-config/type/{type}`

**接口描述**: 根据虚拟电厂类型查询列表

**路径参数**:
- `type`: 虚拟电厂类型（必填，Integer类型，1-聚合型，2-协调型，3-混合型）

**响应示例**:

```json
{
  "code": 0,
  "msg": "执行成功",
  "data": [
    {
      "id": 1,
      "vpp_name": "聚合型虚拟电厂1",
      "vpp_code": "VPP_AGG_001",
      "province": 110000,
      "vpp_type": "聚合型",
      "description": "聚合型虚拟电厂示例",
      "user_count": 5,
      "resource_count": 10,
      "adjustable_capacity": 800.0,
      "picture": null,
      "createtime": 1640995200000,
      "operatorcode": "OP001",
      "create_time": 1640995200000,
      "update_time": 1640995200000,
      "vpp_user_list": [],
      "children": []
    }
  ]
}
```

### 7. 检查虚拟电厂编码是否存在

**接口地址**: `GET /vpp/api/v1/resource-manager/vpp-config/check-code/{code}`

**接口描述**: 检查虚拟电厂编码是否已存在

**路径参数**:
- `code`: 虚拟电厂编码（必填，String类型）

**响应示例**:

```json
{
  "code": 0,
  "msg": "执行成功",
  "data": true    // true表示编码已存在，false表示编码不存在
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 数据字典

### VPP类型

| 值 | 说明 |
|----|------|
| 1 | 聚合型 |
| 2 | 协调型 |
| 3 | 混合型 |

### VPP状态

| 值 | 说明 |
|----|------|
| 1 | 正常 |
| 2 | 维护 |
| 3 | 故障 |

## 注意事项

1. 所有时间字段都使用时间戳格式（毫秒）
2. 编码字段只能包含大写字母、数字和下划线
3. 手机号必须是11位数字，以1开头
4. 邮箱必须符合标准邮箱格式
5. 分页查询时，pageNum从1开始
6. 容量字段单位为MW（兆瓦）
7. 删除操作为物理删除，请谨慎操作

## 示例代码

### JavaScript (Axios)

```javascript
// 创建虚拟电厂
const createVpp = async (vppData) => {
  try {
    const response = await axios.post('/vpp/api/v1/resource-manager/vpp-config', vppData);
    return response.data;
  } catch (error) {
    console.error('创建虚拟电厂失败:', error);
    throw error;
  }
};

// 分页查询虚拟电厂
const getVppPage = async (queryParams) => {
  try {
    const response = await axios.post('/vpp/api/v1/resource-manager/vpp-config/page', queryParams);
    return response.data;
  } catch (error) {
    console.error('查询虚拟电厂列表失败:', error);
    throw error;
  }
};

// 根据ID查询虚拟电厂
const getVppById = async (id) => {
  try {
    const response = await axios.get(`/vpp/api/v1/resource-manager/vpp-config/${id}`);
    return response.data;
  } catch (error) {
    console.error('查询虚拟电厂详情失败:', error);
    throw error;
  }
};
```

### Java (Spring RestTemplate)

```java
// 创建虚拟电厂
public Result<VirtualpowerplantDTO> createVpp(VppDTO vppDTO) {
    String url = "http://localhost:8080/vpp/api/v1/resource-manager/vpp-config";
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    
    HttpEntity<VppDTO> request = new HttpEntity<>(vppDTO, headers);
    
    ParameterizedTypeReference<Result<VirtualpowerplantDTO>> responseType = 
        new ParameterizedTypeReference<Result<VirtualpowerplantDTO>>() {};
    
    ResponseEntity<Result<VirtualpowerplantDTO>> response = 
        restTemplate.exchange(url, HttpMethod.POST, request, responseType);
    
    return response.getBody();
}
```

---

**文档版本**: v1.0  
**最后更新**: 2024-01-01  
**维护人员**: AI-Generated