## 1. 概述

### 1.1 项目背景

虚拟电厂平台通过先进信息通信技术和软件系统，实现分布式电源、储能系统、可控负荷、电动汽车等分布式能源资源的聚合和协调优化，作为特殊电厂参与电力市场和电网运行。

### 1.2 模块功能

资源管理模块实现虚拟电厂、用户、资源、站点、设备的层级化管理，支持增删改查操作，并提供数据关联配置功能。

使用融合平台，资源管理模块是一个插件：vpp-resource-manager

### 1.3 技术栈

- **后端**: JDK8 + Spring Boot 2.x +融合平台
- **前端**: Vue 2 + js+ Element  + vuex+css+融合平台
- **其他**: Redis、RabbitMQ、Docker
- **主备**：cluster-assistant
- **系统**：麒麟2303

## 2. 系统架构设计

### 2.1 整体架构

```mermaid
graph TB
    subgraph "前端层"
        A[Vue2 + javaScript]
        B[Element  UI]

    end
    
    subgraph "网关层"
        D[Spring Cloud Gateway]
    end
    
    subgraph "应用层"
        E[Controller 控制层]
        F[Service 业务层]
    end

    

    
    A --> D
    D --> E
    E --> F
  
   
  
```

### 2.2 分层设计

| 层级   | 职责               | 主要组件                |
| ------ | ------------------ | ----------------------- |
| 表现层 | 用户交互、数据展示 | Vue组件、路由、状态管理 |
| 控制层 | 请求处理、参数校验 | Controller、拦截器      |
| 业务层 | 业务逻辑、事务管理 | Service、业务规则       |
| 数据层 | 数据访问、缓存管理 | Mapper、Redis、数据库   |

## 3. 数据模型设计

### 3.1 实体关系图

```mermaid
erDiagram
    VPP ||--o{ USER : contains
    USER ||--o{ RESOURCE : owns
    RESOURCE ||--o{ SITE : contains
    SITE ||--o{ DEVICE : contains
    DEVICE ||--o{ DATA_POINT : associates
    
    VPP {
        bigint id PK
        string vpp_name
        string province
        string vpp_type
        string description
        int user_count
        int resource_count
        decimal adjustable_capacity
        datetime create_time
        datetime update_time
        boolean deleted
    }
    
    USER {
        bigint id PK
        bigint vpp_id FK
        string user_id
        string user_name
        string user_type
        string region
        string contact_info
        int resource_count
        datetime create_time
        datetime update_time
        boolean deleted
    }
    
    RESOURCE {
        bigint id PK
        bigint user_id FK
        string resource_id
        string resource_name
        string resource_type
        string electricity_user_numbers
        decimal registered_capacity
        string resource_category
        string response_mode
        string resource_status
        boolean platform_direct_control
        string region
        decimal max_up_rate
        decimal max_down_rate
        int site_count
        datetime create_time
        datetime update_time
        boolean deleted
    }
    
    SITE {
        bigint id PK
        bigint resource_id FK
        string site_id
        string site_name
        string site_type
        string site_address
        decimal longitude
        decimal latitude
        int device_count
        datetime create_time
        datetime update_time
        boolean deleted
    }
    
    DEVICE {
        bigint id PK
        bigint site_id FK
        string device_id
        string device_name
        string device_type
        string device_sub_type
        string device_status
        string manufacturer
        string model
        decimal rated_power
        date installation_date
        datetime create_time
        datetime update_time
        boolean deleted
    }
    
    DATA_POINT {
        bigint id PK
        bigint device_id FK
        string point_id
        string point_name
        string point_type
        string data_type
        string unit
        boolean is_controllable
        datetime create_time
        datetime update_time
        boolean deleted
    }
```

### 3.2 核心实体设计

#### 3.2.1 虚拟电厂实体 (vpp_virtualpowerplant)

| 字段名                                | 类型    | 长度 | 必填 | 描述                 |
| ------------------------------------- | ------- | ---- | ---- | -------------------- |
| id                                    | bigint  | -    | 是   | 主键ID               |
| vpp_name                              | varchar | 100  | 是   | 虚拟电厂名称         |
| province                              | varchar | 50   | 是   | 所属省份             |
| vpp_type                              | varchar | 20   | 是   | 类型(调节型/能量型)  |
| description                           | text    | -    | 否   | 描述信息             |
| user_count                            | int4    | -    | 否   | 用户数量             |
| resource_count                        | int4    | -    | 否   | 资源数量             |
| adjustable_capacity                   | decimal | 10,2 | 否   | 可调节容量           |
| picture                               | varchar | 200  | 否   | 图片                 |
| createtime                            | int8    |      | 是   | 成立时间             |
| operatorcode                          | varchar | 200  | 是   | 运营商编号           |
| demand_response_filing_price_caps     | double  |      | 否   | 需求响应申报价格上限 |
| demand_response_declared_price_caps   | double  |      | 否   | 需求响应申报价格下限 |
| peaking_response_declared_price_floor | double  |      | 否   | 调峰响应申报价格下限 |
| peaking_response_filing_price_floor   | double  |      | 否   | 调峰响应申报价格上限 |
| pm_response_declared_price_floor      | double  |      | 否   | 调频响应申报价格下限 |
| pm_response_filing_price_floor        | double  |      |      | 调频响应申报价格上限 |

**业务规则**：

- 虚拟电厂名称在系统内唯一
- 删除时需检查是否存在关联用户
- 自动统计下级用户和资源数量

#### 3.2.2 用户实体 (vpp_user)

| 字段名         | 类型    | 长度 | 必填 | 描述     |
| -------------- | ------- | ---- | ---- | -------- |
| id             | bigint  | -    | 是   | 主键ID   |
| phone_number   | int8    |      |      | 电话     |
| contact_person | varchar | 200  |      | 联系人   |
| user_name      | varchar | 100  | 是   | 用户名称 |
| address        | varchar | 250  | 是   | 地址     |
| region         | varchar | 100  | 否   | 所属区域 |
| resource_count | int     | -    | 否   | 资源数量 |
| city           | int8    |      | 是   | 所属市   |
| district       | int8    |      | 是   | 所属区   |

**业务规则**：

- 用户ID系统自动生成，保证唯一性
- 删除时需检查是否存在关联资源
- 自动统计下级资源数量
- 用户在一个场站下唯一

#### 3.2.3 资源实体 (vpp_resource)

| 字段名                   | 类型    | 长度 | 必填 | 描述                     |
| ------------------------ | ------- | ---- | ---- | ------------------------ |
| id                       | bigint  | -    | 是   | 主键ID                   |
| resource_name            | varchar | 100  | 是   | 资源名称                 |
| resource_type            | varchar | 20   | 是   | 资源类型(发电/储电/用电) |
| electricity_user_numbers | text    | -    | 否   | 用电户号                 |
| registered_capacity      | decimal | 10,2 | 否   | 报装容量                 |
| response_mode            | varchar | 20   | 否   | 响应方式(自动/手动)      |
| resource_status          | varchar | 20   | 是   | 资源状态                 |
| platform_direct_control  | boolean | -    | 否   | 平台直控                 |
| maximum_operable_power   | double  |      |      | 最大可运行功率           |
| minmum_operable_power    | double  |      |      | 最小可运行功率           |
| maximum_upscaling_rate   | double  |      |      | 最大上调速率             |
| Maximum_downward_rate    | double  |      |      | 最大下调速率             |
| longitude                | double  |      |      | 经度                     |
| latitude                 | double  |      |      | 纬度                     |
| contact_person           | varchar |      |      | 联系人                   |
| phone_number             | varchar |      |      | 联系电话                 |
| city                     | int8    |      | 是   | 所属市                   |
| district                 | int4    |      |      | 区域                     |
| address                  | varhcar |      |      | 地址                     |
| site_count               | int     | -    | 否   | 站点数量                 |

**枚举定义**：

- 资源类型：n 发电资源、储电资源、用电资源、微电网资源
- 资源状态：投运、测试、退役、停机
- 响应方式：自动响应、手动响应

#### 3.2.4 站点实体 (vpp_site)

| 字段名         | 类型    | 长度 | 必填 | 描述         |
| -------------- | ------- | ---- | ---- | ------------ |
| id             | bigint  | -    | 是   | 主键ID       |
| resource_id    | bigint  | -    | 是   | 资源ID       |
| longitude      | double  |      |      | 经度         |
| latitude       | double  |      |      | 纬度         |
| contact_person | varchar |      |      | 联系人       |
| phone_number   | varchar |      |      | 联系电话     |
| site_name      | varchar | 100  | 是   | 站点名称     |
| site_type      | varchar | 10   | 是   | 站点类型编码 |
| site_address   | varchar | 200  | 否   | 站点地址     |
| device_count   | int     | -    | 否   | 设备数量     |
| room_id        | int8    |      | 是   | 对应房间id   |

**站点类型编码**：

- 01: 自备电源
- 02: 用户侧储能
- 03: 电动汽车
- 04: 充电站
- 05: 换电站
- 06: 楼宇空调
- 07: 工商业可调节负荷
- 08: 分布式光伏
- 09: 分散式风电
- 10: 分布式独立储能
- 11: 非可调节负荷
- 99: 其他

#### 3.2.5 设备实体 (vpp_device)

| 字段名      | 类型    | 长度 | 必填 | 描述     |
| ----------- | ------- | ---- | ---- | -------- |
| id          | int8    | -    | 是   | 主键ID   |
| site_id     | int8    | -    | 是   | 站点ID   |
| device_id   | varchar | 50   | 是   | 设备编号 |
| device_name | varchar | 100  | 是   | 设备名称 |
| device_type | varchar | 50   | 是   | 设备类型 |

#### 虚拟电厂设备类型表 enum(vpp_device_type)

| id   | 中文名称    | English Translation | 描述（Description）  |
| :--- | :---------- | :------------------ | :------------------- |
| 1    | 光伏设备    | photovoltaic        | 光伏发电相关设备     |
| 2    | 储能设备    | energy_storage      | 能量存储相关设备     |
| 3    | 风机泵类    | fans_pumps          | 风机和泵类设备       |
| 4    | 风冷式空调) | hvac                | 空调系统相关设备     |
| 8    | 充电设备    | `ev_charging`       | 电动汽车充电相关设备 |

#### 资源-站点类型关联关系 (vpp_resource_site_relation)

| resource_type_id | site_type_id |                  |
| :--------------- | :----------- | ---------------- |
| 1                | 1            | 自备电源         |
| 1                | 8            | 分布式光伏       |
| 1                | 9            | 分散式风电       |
| 2                | 2            | 用户侧储能       |
| 2                | 10           | 分布式独立储能   |
| 3                | 3            | 电动汽车         |
| 3                | 4            | 充电站           |
| 3                | 5            | 换电站           |
| 3                | 6            | 楼宇空调         |
| 3                | 7            | 工商业可调节负荷 |
| 3                | 11           | 非可调节负荷     |
| 3                | 99           | 其他             |
| 4                | 1            | 自备电源         |
| 4                | 2            | 用户侧储能       |
| 4                | 3            | 电动汽车         |
| 4                | 4            |                  |
| 4                | 5            |                  |
| 4                | 6            |                  |
| 4                | 7            |                  |
| 4                | 8            |                  |
| 4                | 9            |                  |
| 4                | 10           |                  |
| 4                | 11           |                  |
| 4                | 99           |                  |

#### 站点-设备类型关联关系 (vpp_site_device_relation)

| site_type_id | device_type_id |                              |
| :----------- | :------------- | ---------------------------- |
| 2            | 1              | (分布式光伏 - 光伏设备)      |
| 1            | 2              | (用户侧储能 - 储能设备)      |
| 7            | 3              | (工商业可调节负荷 -风机泵类) |
| 6            | 4              | (楼宇空调- 风冷式空调)       |
| 6            | 5              | 楼宇空调-水冷式制冷站        |
| 6            | 6              | 楼宇空调-分体式空调          |
| 6            | 7              | 楼宇空调-多联机空调          |
| 4            | 8              | 充电站-充电设备              |
| 9            | 9              | 分散式风电-风电设备          |

#### 设备类型与管网类型关系vpp_devicetype_monitorlabel_relation

| 字段         | 类型         | 描述     |
| ------------ | ------------ | -------- |
| devicetype   | int4         | 设备类型 |
| monitorlabel | varchar(100) | 管网类型 |



| 设备类型ID | 设备类型 | 管网设备         | 管网设备表名          | 是否需要新加字段                                             | 备注                                    |
| :--------- | :------- | :--------------- | :-------------------- | ------------------------------------------------------------ | :-------------------------------------- |
| 2          | 储能设备 | PCS变流器        | pcs                   | 已有字段：额定功率（kW）rated_power； 投运日期 operationdate；安装位置position；<br><br />新增字段：最大运行功率（kW）maxworkingfrequency； 最小运行功率（kW）minworkingfrequency； | NULL                                    |
| 2          | 储能设备 | 储能集装箱       | pv_energycontainer    |                                                              | NULL                                    |
| 1          | 光伏设备 | 逆变器           | pv_inverter           | 已有字段：额定功率（kW）outputpower<br />新增字段：    并网电压（V）（非必填） ；最大运行功率（kW）maxworkingfrequency； 最小运行功率（kW）minworkingfrequency；  投运日期 operationdate；安装位置position； | NULL                                    |
| 1          | 光伏设备 | 直流汇流箱       | pv_dccombinerbox      |                                                              | NULL                                    |
| 1          | 光伏设备 | 气象仪           | pv_meteorograph       | 否                                                           | NULL                                    |
| 8          | 充电设备 | 直流充电桩       | pv_chargingstation    | 存在字段：    投运日期 operationdate；安装位置position；<br /> 新增字段：最大运行功率（kW）maxworkingfrequency； 最小运行功率（kW）minworkingfrequency；额定功率（kW）ratedCapacity； | chargemod=1                             |
| 8          | 充电设备 | 交流充电桩       | pv_chargingstation    | 存在字段：    投运日期 operationdate；安装位置position；<br /> 新增字段：最大运行功率（kW）maxworkingfrequency； 最小运行功率（kW）minworkingfrequency；额定功率（kW）ratedCapacity； | chargemod=2                             |
| 2          | 储能设备 | 蓄电池           | battery               | 已有字段： 额定功率（kW）ratedCapacity；  投运日期 operationdate；安装位置position；<br/>新增字段：  额定容量（kWh） 充放电倍率（C） 充电效率 放电效率 | NULL                                    |
| 10         | 用能设备 | 提升泵           | pump                  | 存在字段：  额定功率（kW）rated_power；  投运日期 operationdate；安装位置position；最高频率（Hz）maxworkingfrequency； 最低频率（Hz）minworkingfrequency；<br />新增字段： maxworkingfrequency； 最小运行功率（HZ）minworkingfrequency | 离心泵/柱塞泵/螺杆泵/转子泵/滑片泵/其他 |
| 10         | 用能设备 | 鼓风机           | blowers（新）         |                                                              | NULL                                    |
| 4          | 空调设备 | 风机盘管         | fan_coil（新）        | 否                                                           | NULL                                    |
| 4          | 空调设备 | 变频风冷热泵机组 | heat_pump_units（新） |                                                              |                                         |

#### 新增设备类型

##### **1. 变频风冷热泵机组 (`heat_pump_units`)**

| 字段名                       | 类型    | 长度   | 必填 | 描述                     |
| :--------------------------- | :------ | :----- | :--- | :----------------------- |
| id                           | bigint  | -      | 是   | 主键ID                   |
| device_name                  | varchar | 100    | 是   | 设备名称                 |
| cooling_capacity             | decimal | (10,2) | 是   | 制冷量（kW）             |
| heating_capacity             | decimal | (10,2) | 是   | 制热量（kW）             |
| cooling_capacity_consumption | decimal | (10,2) | 是   | 制冷消耗总功率（kW）     |
| heating_capacity_consumption | decimal | (10,2) | 是   | 制热消耗总功率（kW）     |
| cooling_cop                  | decimal | (5,2)  | 是   | 制冷COP                  |
| heating_cop                  | decimal | (5,2)  | 是   | 制热COP                  |
| iplv                         | decimal | (5,2)  | 否   | 综合部分负荷性能参数IPLV |
| max_working_frequency        | decimal | (10,2) | 是   | 最大运行功率（kW）       |
| min_working_frequency        | decimal | (10,2) | 是   | 最小运行功率（kW）       |
| operation_date               | date    | -      | 是   | 投运日期                 |
| position                     | varchar | 255    | 是   | 安装位置                 |

##### **2. 风机盘管 (`fan_coil`)**

| 字段名         | 类型    | 长度 | 必填 | 描述     |
| :------------- | :------ | :--- | :--- | :------- |
| id             | bigint  | -    | 是   | 主键ID   |
| device_name    | varchar | 100  | 是   | 设备名称 |
| operation_date | date    | -    | 是   | 投运日期 |
| position       | varchar | 255  | 是   | 安装位置 |

##### **3. 鼓风机 (`blowers`)**

| 字段名         | 类型    | 长度   | 必填 | 描述               |
| :------------- | :------ | :----- | :--- | :----------------- |
| id             | bigint  | -      | 是   | 主键ID             |
| device_name    | varchar | 100    | 是   | 设备名称           |
| rated_capacity | decimal | (10,2) | 是   | 额定功率（kW）     |
| max_capacity   | decimal | (10,2) | 是   | 最大运行功率（kW） |
| min_capacity   | decimal | (10,2) | 是   | 最小运行功率（kW） |
| max_frequency  | decimal | (5,2)  | 是   | 最高频率（Hz）     |
| min_frequency  | decimal | (5,2)  | 是   | 最低频率（Hz）     |
| operation_date | date    | -      | 是   | 投运日期           |
| position       | varchar | 255    | 是   | 安装位置           |

## 4. 接口设计

### 4.1 RESTful API 规范

#### 4.1.1 URL 设计规范

- 基础路径：/vpp/api/v1/resource-manager/
- 资源命名：使用名词复数形式
- 层级关系：通过URL路径体现

#### 4.1.2 HTTP 方法规范

- GET：查询操作
- POST：创建操作
- PUT：完整更新操作
- PATCH：部分更新操作
- DELETE：删除操作

#### 4.1.3 响应格式规范

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-06-17T10:30:00"
}
```

### 4.2 核心接口设计

#### 4.2.1 虚拟电厂管理接口

| 方法   | 路径                                                | 描述                     |
| ------ | --------------------------------------------------- | ------------------------ |
| POST   | /vpp/resource-manager/vpp-config                    | 创建虚拟电厂             |
| GET    | /vpp/resource-manager/vpp-config/{id}               | 获取虚拟电厂详情         |
| PUT    | /vpp/resource-manager/vpp-config/{id}               | 更新虚拟电厂             |
| DELETE | /vpp/resource-manager/vpp-config/{id}               | 删除虚拟电厂             |
| POST   | /vpp/resource-manager/vpp-config/page               | 分页查询虚拟电厂列表     |
| GET    | /vpp/resource-manager/vpp-config/list               | 查询所有虚拟电厂列表     |
| GET    | /vpp/resource-manager/vpp-config/type/{type}        | 根据类型查询虚拟电厂列表 |
| GET    | /vpp/resource-manager/vpp-config/check-code/{code}  | 检查虚拟电厂编码是否存在 |
| GET    | /vpp/resource-manager/vpp-config/count              | 统计虚拟电厂数量         |

#### 4.2.2 用户管理接口

| 方法   | 路径                                                | 描述                 |
| ------ | --------------------------------------------------- | -------------------- |
| POST   | /vpp/api/v1/resource-manager/user                   | 创建用户             |
| GET    | /vpp/api/v1/resource-manager/user/{id}              | 获取用户详情         |
| GET    | /vpp/api/v1/resource-manager/user/username/{username} | 根据用户名查询用户   |
| PUT    | /vpp/api/v1/resource-manager/user/{id}              | 更新用户             |
| DELETE | /vpp/api/v1/resource-manager/user/{id}              | 删除用户             |
| POST   | /vpp/api/v1/resource-manager/user/page              | 分页查询用户列表     |
| GET    | /vpp/api/v1/resource-manager/user/list              | 查询所有用户列表     |
| GET    | /vpp/api/v1/resource-manager/user/vpp/{vppId}       | 根据VPP查询用户列表  |

#### 4.2.3 资源管理接口

| 方法   | 路径                                              | 描述                 |
| ------ | ------------------------------------------------- | -------------------- |
| POST   | /vpp/resource-manager/resource-config             | 创建资源             |
| GET    | /vpp/resource-manager/resource-config/{id}        | 获取资源详情         |
| PUT    | /vpp/resource-manager/resource-config/{id}        | 更新资源             |
| DELETE | /vpp/resource-manager/resource-config/{id}        | 删除资源             |
| POST   | /vpp/resource-manager/resource-config/page        | 分页查询资源列表     |
| GET    | /vpp/resource-manager/resource-config/list        | 获取所有资源列表     |
| GET    | /vpp/resource-manager/resource-config/status/{status} | 根据状态获取资源列表 |
| GET    | /vpp/resource-manager/resource-config/check-code/{code} | 检查资源编码是否存在 |

#### 4.2.4 站点管理接口

| 方法   | 路径                                        | 描述                 |
| ------ | ------------------------------------------- | -------------------- |
| POST   | /vpp/resource-manager/site-config           | 创建站点             |
| GET    | /vpp/resource-manager/site-config/{id}      | 获取站点详情         |
| PUT    | /vpp/resource-manager/site-config/{id}      | 更新站点             |
| DELETE | /vpp/resource-manager/site-config/{id}      | 删除站点             |
| POST   | /vpp/resource-manager/site-config/page      | 分页查询站点列表     |
| GET    | /vpp/resource-manager/site-config/list      | 查询所有站点列表     |
| GET    | /vpp/resource-manager/site-config/vpp/{vppId} | 根据VPP查询站点列表 |
| GET    | /vpp/resource-manager/site-config/types     | 获取站点类型列表     |

#### 4.2.5 设备管理接口

| 方法   | 路径                                            | 描述                   |
| ------ | ----------------------------------------------- | ---------------------- |
| POST   | /vpp/resource-manager/device-config             | 创建设备               |
| GET    | /vpp/resource-manager/device-config/{id}        | 获取设备详情           |
| PUT    | /vpp/resource-manager/device-config/{id}        | 更新设备               |
| DELETE | /vpp/resource-manager/device-config/{id}        | 删除设备               |
| GET    | /vpp/resource-manager/device-config             | 分页查询设备列表       |
| GET    | /vpp/resource-manager/device-config/site/{siteId} | 获取站点下设备列表     |
| GET    | /vpp/resource-manager/device-config/check-id    | 检查设备编号是否存在   |

#### 4.2.6 VPP层级树缓存管理接口

| 方法   | 路径                                                | 描述                       |
| ------ | --------------------------------------------------- | -------------------------- |
| GET    | /api/vpp/tree/cache/{vppId}                         | 获取VPP树                  |
| PUT    | /api/vpp/tree/cache/{vppId}/refresh                 | 刷新VPP树缓存              |
| DELETE | /api/vpp/tree/cache/{vppId}                         | 删除VPP树缓存              |
| GET    | /api/vpp/tree/cache/all                             | 获取所有VPP树              |
| GET    | /api/vpp/tree/cache/{vppId}/nodes/{nodeType}        | 根据类型获取节点           |
| GET    | /api/vpp/tree/cache/{vppId}/search?keyword={keyword}&nodeType={nodeType} | 搜索节点 |
| GET    | /api/vpp/tree/cache/{vppId}/path?nodeId={nodeId}&nodeType={nodeType} | 查找节点路径 |
| POST   | /api/vpp/tree/cache/batch/refresh                   | 批量刷新缓存               |

#### 4.2.7 基础配置管理接口

| 方法   | 路径                                                | 描述                           |
| ------ | --------------------------------------------------- | ------------------------------ |
| GET    | /api/v1/base-config/site-device-relations          | 获取站点-设备类型关联关系      |

### 4.3 新增接口详细说明

#### 4.3.1 用户管理新增接口

##### 根据用户名查询用户
- **路径**: `GET /vpp/api/v1/resource-manager/user/username/{username}`
- **参数**:
  - `username`: 用户名（路径参数，必填）
- **返回**: `Result<VppUserBO>`
- **说明**: 根据用户名查询用户详细信息

#### 4.3.2 资源管理新增接口

##### 根据状态获取资源列表
- **路径**: `GET /vpp/resource-manager/resource-config/status/{status}`
- **参数**:
  - `status`: 资源状态（路径参数，必填）
- **返回**: `Result<List<VppResourceBO>>`
- **说明**: 根据资源状态获取资源列表

##### 检查资源编码是否存在
- **路径**: `GET /vpp/resource-manager/resource-config/check-code/{code}`
- **参数**:
  - `code`: 资源编码（路径参数，必填）
- **返回**: `Result<Boolean>`
- **说明**: 检查资源编码是否已存在，用于编码唯一性验证

#### 4.3.3 站点管理新增接口

##### 获取站点类型列表
- **路径**: `GET /vpp/resource-manager/site-config/types`
- **参数**: 无
- **返回**: `Result<List<Map<String, Object>>>`
- **说明**: 获取所有可用的站点类型列表，包含类型编码和名称

#### 4.3.4 设备管理接口修正

##### 分页查询设备列表
- **路径**: `GET /vpp/resource-manager/device-config`（修正为GET方法）
- **参数**: `DeviceQueryDTO`（查询参数）
- **返回**: `Result<PageResult<VppDeviceBO>>`
- **说明**: 根据条件分页查询设备列表

##### 检查设备编号是否存在
- **路径**: `GET /vpp/resource-manager/device-config/check-id`
- **参数**:
  - `deviceId`: 设备编号（查询参数，必填）
  - `excludeId`: 排除的ID（查询参数，可选）
- **返回**: `Result<Boolean>`
- **说明**: 检查设备编号是否已存在，支持排除指定ID

#### 4.3.5 VPP树缓存管理接口补充

##### 搜索节点
- **路径**: `GET /api/vpp/tree/cache/{vppId}/search`
- **参数**:
  - `vppId`: VPP ID（路径参数，必填）
  - `keyword`: 搜索关键词（查询参数，必填）
  - `nodeType`: 节点类型（查询参数，可选）
- **返回**: `Result<List<TreeNodeDTO>>`
- **说明**: 根据关键词搜索节点，支持按节点类型过滤

##### 查找节点路径
- **路径**: `GET /api/vpp/tree/cache/{vppId}/path`
- **参数**:
  - `vppId`: VPP ID（路径参数，必填）
  - `nodeId`: 节点ID（查询参数，必填）
  - `nodeType`: 节点类型（查询参数，必填）
- **返回**: `Result<List<TreeNodeDTO>>`
- **说明**: 查找从根节点到指定节点的完整路径



### 4.4 请求响应模型设计

#### 4.4.1 通用响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-06-17T10:30:00"
}
```

#### 4.4.2 分页查询模型

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "index": 0,
    "limit": 10,
    "total": 100,
    "records": []
  }
}
```

#### 4.4.3 虚拟电厂数据模型

##### VppDTO（创建/更新请求）

```json
{
  "name": "湖北虚拟电厂",
  "code": "VPP_HUBEI_001",
  "type": 1,
  "status": 1,
  "location": "湖北省武汉市",
  "manager": "张三",
  "description": "湖北省虚拟电厂示例",
  "capacity": 1000.50,
  "operatingMode": "自动",
  "contactInfo": "13800138000"
}
```

##### VirtualpowerplantBO（响应数据）

```json
{
  "id": 1,
  "vppName": "湖北虚拟电厂",
  "vppCode": "VPP_HUBEI_001",
  "province": 42,
  "vppType": "调节型",
  "description": "湖北省虚拟电厂示例",
  "userCount": 10,
  "resourceCount": 50,
  "adjustableCapacity": 1000.50,
  "picture": "http://example.com/vpp.jpg",
  "createtime": 1640995200000,
  "operatorcode": "OP001",
  "demandResponseFilingPriceCaps": 100.0,
  "demandResponseDeclaredPriceCaps": 80.0,
  "peakingResponseDeclaredPriceFloor": 50.0,
  "peakingResponseFilingPriceFloor": 70.0,
  "pmResponseDeclaredPriceFloor": 30.0,
  "pmResponseFilingPriceFloor": 40.0,
  "createTime": "2024-01-01T10:00:00",
  "updateTime": "2024-01-01T10:00:00",
  "vppUserList": [],
  "children": []
}
```

##### VppQueryDTO（查询条件）

```json
{
  "pageNum": 1,
  "pageSize": 10,
  "name": "湖北",
  "code": "VPP_HUBEI",
  "type": 1,
  "status": 1,
  "location": "湖北省",
  "manager": "张三",
  "sortField": "createTime",
  "sortOrder": "desc"
}
```

#### 4.4.4 用户数据模型

##### VppUserDTO（创建/更新请求）

```json
{
  "id": 1,
  "userName": "用户A",
  "vppId": 1,
  "phoneNumber": "13800138000",
  "contactPerson": "李四",
  "address": "湖北省武汉市洪山区",
  "region": 1,
  "resourceCount": 5,
  "city": 4201,
  "district": 420111,
  "createTime": "2024-01-01T10:00:00",
  "updateTime": "2024-01-01T10:00:00"
}
```

##### VppUserBO（响应数据）

```json
{
  "id": 1,
  "vppId": 1,
  "phoneNumber": 13800138000,
  "contactPerson": "李四",
  "userName": "用户A",
  "address": "湖北省武汉市洪山区",
  "region": 1,
  "resourceCount": 5,
  "city": 4201,
  "district": 420111,
  "createTime": "2024-01-01T10:00:00",
  "updateTime": "2024-01-01T10:00:00",
  "vppResourceList": [],
  "children": []
}
```

##### UserQueryDTO（查询条件）

```json
{
  "index": 0,
  "limit": 100,
  "userName": "用户A",
  "vppId": 1,
  "phoneNumber": "13800138000",
  "contactPerson": "李四",
  "address": "武汉市",
  "region": 1,
  "city": 4201,
  "district": 420111
}
```

#### 4.4.5 资源数据模型

##### VppResourceDTO（创建/更新请求）

```json
{
  "id": 1,
  "name": "光伏发电资源A",
  "code": "RES_PV_001",
  "type": 1,
  "vppId": 1,
  "siteId": 1,
  "ratedPower": 100.0,
  "currentPower": 80.0,
  "status": 1,
  "location": "湖北省武汉市",
  "model": "PV-1000",
  "manufacturer": "华为",
  "installDate": "2024-01-01T10:00:00",
  "description": "光伏发电设备",
  "createTime": "2024-01-01T10:00:00",
  "updateTime": "2024-01-01T10:00:00",
  "createBy": "admin",
  "updateBy": "admin",
  "deleted": 0
}
```

##### VppResourceBO（响应数据）

```json
{
  "id": 1,
  "userId": 1,
  "resourceName": "光伏发电资源A",
  "resourceType": "发电资源",
  "electricityUserNumbers": "001,002,003",
  "registeredCapacity": 100.0,
  "responseMode": "自动响应",
  "resourceStatus": "投运",
  "platformDirectControl": true,
  "maximumOperablePower": 100.0,
  "minmumOperablePower": 10.0,
  "maximumUpscalingRate": 50.0,
  "maximumDownwardRate": 30.0,
  "longitude": 114.305,
  "latitude": 30.593,
  "contactPerson": "王五",
  "phoneNumber": "13900139000",
  "city": 4201,
  "district": 420111,
  "address": "湖北省武汉市洪山区",
  "siteCount": 3,
  "createTime": "2024-01-01T10:00:00",
  "updateTime": "2024-01-01T10:00:00",
  "vppSiteList": [],
  "children": []
}
```

#### 4.4.6 站点数据模型

##### VppSiteBO（响应数据）

```json
{
  "id": 1,
  "resourceId": 1,
  "longitude": 114.305,
  "latitude": 30.593,
  "contactPerson": "赵六",
  "phoneNumber": "13700137000",
  "siteName": "光伏发电站A",
  "siteType": 8,
  "siteAddress": "湖北省武汉市洪山区光谷大道",
  "deviceCount": 5,
  "roomId": 1001,
  "createTime": "2024-01-01T10:00:00",
  "updateTime": "2024-01-01T10:00:00",
  "vppDeviceList": [],
  "children": []
}
```

##### SiteQueryDTO（查询条件）

```json
{
  "siteId": "SITE_001",
  "siteName": "光伏发电站",
  "siteType": 8,
  "vppId": 1,
  "siteStatus": "运行",
  "location": "武汉市",
  "pageNum": 1,
  "pageSize": 10,
  "orderBy": "createTime desc"
}
```

#### 4.4.7 设备数据模型

##### VppDeviceBO（响应数据）

```json
{
  "id": 1,
  "siteId": 1,
  "deviceId": "DEV_001",
  "deviceName": "PCS变流器1",
  "deviceType": "储能设备",
  "modellabel": "pcs",
  "createTime": "2024-01-01T10:00:00",
  "updateTime": "2024-01-01T10:00:00",
  "pcsList": [],
  "pvEnergyContainerList": [],
  "pvInverterList": [],
  "pvDcCombinerBoxList": [],
  "pvMeteorographList": [],
  "pvChargingStationList": [],
  "batteryList": [],
  "pumpList": [],
  "blowersList": [],
  "fanCoilList": [],
  "heatPumpUnitsList": [],
  "children": []
}
```

#### 4.4.8 VPP层级树数据模型

##### TreeNodeDTO（树节点）

```json
{
  "id": 1,
  "name": "湖北虚拟电厂",
  "label": "vpp",
  "parentId": null,
  "level": 0,
  "hasChildren": true,
  "expanded": false,
  "selected": false,
  "disabled": false,
  "icon": "vpp-icon",
  "tooltip": "虚拟电厂节点",
  "metadata": {
    "type": "vpp",
    "status": "active"
  },
  "children": [
    {
      "id": 2,
      "name": "用户A",
      "label": "user",
      "parentId": 1,
      "level": 1,
      "hasChildren": true,
      "children": []
    }
  ]
}
```

## 5. 前端设计

### 5.1 页面结构设计

```mermaid
graph TB
    A[资源管理主页] --> B[虚拟电厂管理]
    A --> C[用户管理]
    A --> D[资源配置]
    A --> E[站点管理]
    A --> F[设备管理]
    
    B --> B1[虚拟电厂列表]
    B --> B2[新建/编辑虚拟电厂]
    
    C --> C1[用户列表]
    C --> C2[新建/编辑用户]
    
    D --> D1[资源列表]
    D --> D2[资源配置表单]
    
    E --> E1[站点列表]
    E --> E2[站点配置表单]
    
    F --> F1[设备列表]
    F --> F2[设备配置表单]
    F --> F3[数据点关联]
```

### 5.2 组件设计

#### 5.2.1 业务组件

- **VppManagement**：虚拟电厂管理
- **UserManagement**：用户管理
- **ResourceConfig**：资源配置
- **SiteManagement**：站点管理
- **DeviceManagement**：设备管理
- **VppTree**:电厂树

### 5.3 状态管理设计

#### 5.3.1 Store结构

```
resourceStore/
├── state/
│   ├── VppTree          // 虚拟电厂树
├── getters/
│   ├── VppTree       // 虚拟电厂树
└── actions/
    ├── VppTree     // 虚拟电厂树
```

#### 4.4.9 枚举类型定义

##### VPP类型枚举

```json
{
  "1": "聚合型",
  "2": "协调型",
  "3": "混合型"
}
```

##### VPP状态枚举

```json
{
  "1": "正常",
  "2": "维护",
  "3": "故障"
}
```

##### 资源类型枚举

```json
{
  "1": "发电设备",
  "2": "储能设备",
  "3": "负荷设备"
}
```

##### 资源状态枚举

```json
{
  "1": "在线",
  "2": "离线",
  "3": "故障"
}
```

##### 站点类型枚举

```json
{
  "01": "自备电源",
  "02": "用户侧储能",
  "03": "电动汽车",
  "04": "充电站",
  "05": "换电站",
  "06": "楼宇空调",
  "07": "工商业可调节负荷",
  "08": "分布式光伏",
  "09": "分散式风电",
  "10": "分布式独立储能",
  "11": "非可调节负荷",
  "99": "其他"
}
```

##### 设备类型枚举

```json
{
  "1": "光伏设备",
  "2": "储能设备",
  "3": "风机泵类",
  "4": "风冷式空调",
  "8": "充电设备"
}
```

#### 4.4.10 错误响应格式

```json
{
  "code": 400,
  "message": "参数验证失败",
  "data": null,
  "timestamp": "2025-06-17T10:30:00",
  "errors": [
    {
      "field": "name",
      "message": "VPP名称不能为空"
    }
  ]
}
```

## 6. 关键业务流程

### 6.1 虚拟电厂创建流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant DB as 数据库
    participant R as Redis

    U->>F: 填写虚拟电厂信息
    F->>F: 前端表单验证
    F->>B: POST /api/v1/vpp
    B->>B: 参数校验
    B->>DB: 检查名称是否重复
    DB-->>B: 返回查询结果
    
    alt 名称不重复
        B->>DB: 插入虚拟电厂记录
        DB-->>B: 返回插入结果
        B->>R: 清除相关缓存
        B-->>F: 返回成功响应
        F-->>U: 显示创建成功
    else 名称重复
        B-->>F: 返回错误响应
        F-->>U: 显示错误信息
    end
```

### 6.2 层级数据管理流程

```mermaid
graph TD
    A[虚拟电厂] --> B[用户管理]
    B --> C[资源管理]
    C --> D[站点管理]
    D --> E[设备管理]
   
    
    G[统计数量] --> A
    G --> B
    G --> C
    G --> D
    
    H[删除检查] --> A
    H --> B
    H --> C
    H --> D
    
    subgraph "业务规则"
        I[只能删除无下级的节点]
        J[自动生成唯一ID]
        K[自动统计下级数量]
        L[支持批量操作]
    end
```

### 6.3 数据关联配置流程



```mermaid
graph LR
    subgraph "虚拟电厂层级"
        A[虚拟电厂管理]
        B[用户管理]
        C[资源管理]
        D[站点管理]
        E[电厂设备]

    end
    subgraph "管网层级"
        G[房间]
        H[管网设备]
        I[监测设备]
    end
    G --> H
    H --> I
    E --> H
    A --> B
    B --> C
    C --> D
    D --> E

```

虚拟电厂层级的配置都是从做到右是1对多

站点管理需要关联一个房间，只是一个映射；在创建站点时，选择对应房间

管网设备对电厂设备供能

#### 供能关系配置：

管网设备对电厂设备供能

电厂设备对站点供能

站点对资源供能

资源对用户供能

用户对虚拟电厂供能

光伏，储能插件使用quantityaggregationdata

问题：

1.电厂设备的属性有哪些？

2.电厂设备关联多个管网设备，管网设备有的属性（额定功率，最大运行功率等）累加到电厂设备吗？

3.子设备对应管网不全

4.配电类型的子类型有哪些？



### 6.4 数据统计更新流程

```mermaid
flowchart LR
    A[数据变更事件] --> B{变更类型}
    
    B -->|新增| C[增加计数]
    B -->|删除| D[减少计数]
    B -->|修改| E[检查状态变化]
    
    C --> F[向上级传播]
    D --> F
    E --> F
    
    F --> G[更新父级统计]
    G --> H[触发缓存更新]
    H --> I[发送变更通知]
```

## 