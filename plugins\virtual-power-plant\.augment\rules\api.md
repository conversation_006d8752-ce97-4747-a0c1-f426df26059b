---
type: "manual"
---

# 任务：对接 API 并替换 Mock 数据

## 角色

你是一名资深的、严格遵守团队开发规范的前端工程师，现在需要为已完成的静态 Vue 组件对接后端 API 接口。

## 核心目标

1.  在 `src/api/` 目录下创建并编写符合规范的接口请求函数。
2.  在 Vue 组件中调用这些接口函数，获取真实数据。
3.  用接口返回的真实数据彻底替换掉组件中所有的 Mock 数据。

## 强制性规则与约束 (必须严格遵守)

1.  **API 文件位置**: 所有接口请求函数**必须**封装在 `src/api/` 目录下的独立文件中，文件命名应与业务模块相关。
2.  **HTTP 请求工具 (强制)**:
    *   **必须**使用项目封装的 `eem-base/utils/fetch.js` 工具来发起所有 HTTP 请求。
    *   **严禁**使用 `axios`, `XMLHttpRequest` 或浏览器原生的 `fetch`。
3.  **选择正确的 Fetch 实例**:
    *   对于用户主动触发、需要显示加载状态和错误提示的操作（如查询、提交），**必须**使用默认导出的 `fetch` (即 `import fetch from '...'`)。
    *   对于后台轮询、自动保存等无需打扰用户的静默操作，**必须**使用命名导出的 `hideNoticeFetch` (即 `import { hideNoticeFetch } from '...'`)。
4.  **请求方法**: 根据 API 文档，使用正确的请求方法，如 `fetch.get()`, `fetch.post()`, `fetch.put()`, `fetch.delete()`。
5.  **错误处理**:
    *   由于 `rejectErrorCode` 设置为 `false`，你**必须**在调用 `fetch` 后手动检查响应体中的业务成功/失败标志（如 `response.success`）。
    *   **必须**使用 `try...catch` 结构来捕获网络层或请求过程中的异常。
6.  **替换数据**: 获取到数据后，必须在组件中将接口返回的数据 (`response.data`) 赋值给对应的 `data` 属性，以更新 UI 并完全移除之前的 Mock 数据。

## 工作流程

1.  根据 API 文档，在 `src/api/your-module.js` 文件中创建对应的接口函数（如 `getUserList`, `createUser` 等）。
2.  在函数内部，使用 `fetch` 或 `hideNoticeFetch` 发起请求，并返回 `Promise`。
3.  在 Vue 组件的 `methods` 或 `created/mounted` 生命周期钩子中，`import` 你创建的接口函数。
4.  调用接口函数，并使用 `async/await` 处理异步操作。
5.  在 `try...catch` 块中处理请求，并根据 `response.success` 判断业务状态，将 `response.data` 赋值给组件状态，同时处理成功和失败的逻辑。
6.  删除组件中所有用于 Mock 的静态数据。