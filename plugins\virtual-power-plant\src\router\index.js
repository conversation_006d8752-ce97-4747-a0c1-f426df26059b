/** @type {import("vue-router").RouteConfig[]} */

import main from "./main.vue";

export const appRoutes = [
  {
    path: "/",
    component: main,
    children: [
      {
        meta: {
          keepAlive: true
        },
        path: "/aggregation",
        component: () =>
          import("@/projects/vpp-resource-manager/aggregation/index.vue")
      },
      {
        meta: {
          keepAlive: true
        },
        path: "/resource-config",
        component: () =>
          import("@/projects/vpp-resource-manager/resource-config/index.vue")
      },
      {
        meta: {
          keepAlive: true
        },
        path: "/data-management",
        component: () =>
          import(
            "@/projects/vpp-resource-manager/virtual-power-plant-config/index.vue"
          )
      }
    ]
  }
];

export default {
  // mode: "history",
  routes: appRoutes
};
