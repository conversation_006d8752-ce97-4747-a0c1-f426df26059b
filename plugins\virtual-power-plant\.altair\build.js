module.exports = {
  pluginInfos: {
    // frontVersion: "1.0.0",//可自定义前端部分的版本号，如果使用package.json中的版本号，则可以不填
    version: "1.0.0", //整体插件的版本号，必填
    name: "vppresourcemanager" //整体插件的唯一值，必填
  },
  market: {
    host: "http://************:8190",
    user: {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      password: "sA123456@"
    }
  },
  build: {
    // command: "vite build --outDir {{outputDir}}"
    // command: "vue-cli-service build --dest {{outputDir}}"//默认的命令
  },
  ding: {
    //是否发送钉钉消息，默认不发送
    send: false,
    // proxy: "http://*************:9898",
    proxy: "",
    robots: [
      {
        secret:
          "SEC0b6f90cc3b7891b787288e7c77ce8148dfa010326045a6c3248935e3f3c6c2ba",
        webhook:
          "https://oapi.dingtalk.com/robot/send?access_token=a8ccb0d67d745e82f602560329fdd39cebea2fda2c098fed515312ab1ad3a0f9"
      }
    ],
    isAtAll: false,
    // 通过手机号@相关人
    // atMobiles: ["18062123947"]
    atMobiles: []
  }
};
