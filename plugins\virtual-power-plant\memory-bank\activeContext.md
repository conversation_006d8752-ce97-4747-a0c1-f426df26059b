# Active Context

## 当前工作重点

- vpp-resource-manager 插件完整重构：按照设计文档实现虚拟电厂资源管理模块的完整功能架构。
- 已完成 API 接口层、状态管理层、组件架构层的全面重构。
- 统一使用 cet 组件体系，确保 UI 风格一致性和开发效率。

## 最新变更

### vpp-resource-manager 插件重构完成

- **API 接口层**：新增 `src/api/vpp-resource.js`，实现完整的 RESTful API 接口

  - 虚拟电厂、用户、资源、站点、设备、数据点的 CRUD 操作
  - 支持分页查询、层级数据获取
  - 遵循 RESTful 规范，统一响应格式

- **状态管理层**：新增 `src/store/modules/vppResource.js`，注册到 Vuex

  - 统一管理虚拟电厂、用户、资源、站点、设备数据状态
  - 实现异步数据获取和缓存管理
  - 支持 loading 状态和错误处理

- **组件架构重构**：

  - 主页面 `resource-config/index.vue`：支持多层级管理，动态渲染不同组件
  - 业务组件统一放在 `resource-config/components/` 目录下：
    - `VppTree.vue`：虚拟电厂树形组件
    - `VppManagement.vue`：虚拟电厂管理
    - `UserManagement.vue`：用户管理
    - `ResourceManagement.vue`：资源管理
    - `SiteManagement.vue`：站点管理
    - `DeviceManagement.vue`：设备管理

- **目录结构规范化**：
  - 修正组件目录位置，统一放在 `resource-config/components/`
  - 删除错误的组件目录，确保目录结构清晰

## 技术决策

- **组件体系**：全面采用 cet 组件体系（CetTable、CetButton、CetTree 等）
- **状态管理**：使用 Vuex 进行统一状态管理，支持模块化
- **API 设计**：遵循 RESTful 规范，支持层级数据查询
- **目录规范**：严格遵循团队目录结构规范，组件放在对应业务目录下

## 下一步计划

- 完善各管理组件的具体业务逻辑（增删改查表单、数据关联配置等）
- 实现树形数据的动态生成和层级统计
- 对接真实后端 API，完善数据交互
- 实现数据关联功能（站点与房间、设备与管网设备关联）
- 完善错误处理和用户体验优化

## 架构优势

- **模块化设计**：API、状态、组件分离，便于维护和扩展
- **组件复用**：统一使用 cet 组件，减少重复开发
- **数据流清晰**：Vuex 统一状态管理，数据流向明确
- **目录规范**：严格遵循团队规范，便于团队协作

---

## 【组件用法规范总结】

### CetTable

- 用`.sync`修饰符实现数据双向绑定
- 复杂表格建议用`v-bind`/`v-on`传递配置和事件
- 支持分页、树形、导出、批量操作等

**示例：**

```vue
<CetTable
  :data.sync="tableData"
  :dynamicInput.sync="dynamicInput"
  queryMode="trigger"
  dataMode="component"
  :dataConfig="dataConfig"
  :showPagination="true"
  :border="true"
  :highlightCurrentRow="true"
  @outputData_out="handleDataOutput"
  @currentChange_out="handleCurrentChange"
>
  <el-table-column prop="name" label="姓名"></el-table-column>
  <el-table-column prop="age" label="年龄"></el-table-column>
</CetTable>
```

### CetButton

- 推荐用`title`属性传递按钮文本
- 推荐用`:visible_in`、`:disable_in`控制显示/禁用
- 推荐用`@statusTrigger_out`事件（自动生成时间戳）
- 兼容 ElementUI Button 的`type`、`size`、`icon`等属性

**示例：**

```vue
<CetButton
  :visible_in="true"
  :disable_in="false"
  title="按钮文本"
  size="small"
  type="primary"
  class="自定义样式"
  @statusTrigger_out="handleClick"
/>
```

### CetSimpleSelect

- 必须用`option`属性指定字段映射
- 静态数据用`interface.data`传递，`dataMode: 'static'`
- 推荐用`v-model`实现选中值双向绑定

**示例：**

```vue
<CetSimpleSelect
  v-model="selectedValue"
  :option="{ key: 'value', label: 'label', value: 'value' }"
  :interface="{
    data: optionsArray,
    queryMode: 'trigger',
    dataMode: 'static'
  }"
  placeholder="请选择"
  class="自定义样式"
/>
```
