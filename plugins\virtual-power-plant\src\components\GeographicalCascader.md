# 地理级联选择器 (GeographicalCascader)

## 功能概述

基于`getGeographicalData`接口返回数据的省市区级联选择器组件，支持多种配置模式和使用场景。

## 特性

- ✅ 基于真实地理数据的级联选择
- ✅ 支持省份、城市、区县三级联动
- ✅ 可配置选择层级（1-省份，2-城市，3-区县）
- ✅ 支持单选和多选模式
- ✅ 支持懒加载和预加载模式
- ✅ 支持严格模式和非严格模式
- ✅ 支持显示地区编码
- ✅ 内置缓存机制，性能优化
- ✅ 完整的事件回调
- ✅ 响应式设计
- ✅ 国际化支持

## 基础用法

```vue
<template>
  <GeographicalCascader
    v-model="selectedValue"
    :placeholder="$T('请选择省份/城市/区县')"
    @change="handleChange"
  />
</template>

<script>
import GeographicalCascader from "@/components/GeographicalCascader.vue";

export default {
  components: {
    GeographicalCascader
  },
  data() {
    return {
      selectedValue: []
    };
  },
  methods: {
    handleChange(value) {
      console.log("选中值:", value);
    }
  }
};
</script>
```

## 组件属性 (Props)

| 属性名        | 类型    | 默认值                 | 说明                             |
| ------------- | ------- | ---------------------- | -------------------------------- |
| value         | Array   | []                     | v-model 绑定的值                 |
| placeholder   | String  | '请选择省份/城市/区县' | 占位符文本                       |
| clearable     | Boolean | true                   | 是否可清空                       |
| disabled      | Boolean | false                  | 是否禁用                         |
| size          | String  | 'medium'               | 尺寸：large/medium/small/mini    |
| filterable    | Boolean | true                   | 是否可搜索                       |
| showAllLevels | Boolean | true                   | 是否显示完整路径                 |
| collapseTags  | Boolean | false                  | 多选时是否折叠标签               |
| showCode      | Boolean | false                  | 是否显示地区编码                 |
| level         | Number  | 3                      | 级联层级：1-省份，2-城市，3-区县 |
| multiple      | Boolean | false                  | 是否多选                         |
| checkStrictly | Boolean | false                  | 是否严格模式（只能选择叶子节点） |
| lazy          | Boolean | false                  | 是否懒加载                       |

## 事件

| 事件名         | 参数    | 说明                   |
| -------------- | ------- | ---------------------- |
| change         | value   | 选择改变时触发         |
| node-change    | nodes   | 选中节点信息改变时触发 |
| expand-change  | value   | 展开状态改变时触发     |
| blur           | event   | 失焦时触发             |
| focus          | event   | 聚焦时触发             |
| visible-change | visible | 下拉框显示/隐藏时触发  |
| remove-tag     | value   | 多选时移除标签时触发   |

## 方法

| 方法名              | 参数                                   | 返回值  | 说明                     |
| ------------------- | -------------------------------------- | ------- | ------------------------ |
| getSelectedNodes    | value                                  | Array   | 获取选中的节点信息       |
| getPathByCode       | provinceCode, cityCode?, districtCode? | Array   | 根据编码获取完整路径     |
| getFullNamePath     | value                                  | String  | 获取完整的地区名称路径   |
| getLastLevelName    | value                                  | String  | 获取最后一级的地区名称   |
| validateValue       | value                                  | Boolean | 验证选中的值是否有效     |
| getCitiesByProvince | provinceValue                          | Array   | 获取指定省份下的所有城市 |
| getDistrictsByCity  | cityValue                              | Array   | 获取指定城市下的所有区县 |
| searchByName        | keyword                                | Array   | 根据名称搜索地区         |
| refresh             | -                                      | Promise | 刷新地理数据             |

## 使用场景

### 1. 只选择省份

```vue
<GeographicalCascader
  v-model="provinceValue"
  :level="1"
  :placeholder="$T('请选择省份')"
/>
```

### 2. 只选择到城市

```vue
<GeographicalCascader
  v-model="cityValue"
  :level="2"
  :placeholder="$T('请选择省份/城市')"
/>
```

### 3. 显示地区编码

```vue
<GeographicalCascader
  v-model="codeValue"
  :show-code="true"
  :placeholder="$T('请选择省份/城市/区县')"
/>
```

### 4. 多选模式

```vue
<GeographicalCascader
  v-model="multipleValue"
  :multiple="true"
  :collapse-tags="true"
  :placeholder="$T('请选择多个地区')"
/>
```

### 5. 懒加载模式

```vue
<GeographicalCascader
  v-model="lazyValue"
  :lazy="true"
  :placeholder="$T('请选择地区（懒加载）')"
/>
```

### 6. 非严格模式

```vue
<GeographicalCascader
  v-model="nonStrictValue"
  :check-strictly="true"
  :placeholder="$T('可选择任意级别')"
/>
```

### 7. 工具方法使用

```vue
<template>
  <div>
    <GeographicalCascader
      ref="cascader"
      v-model="selectedValue"
      @change="handleChange"
    />

    <div>
      <p>完整路径：{{ fullPath }}</p>
      <p>最后一级：{{ lastName }}</p>
    </div>

    <el-input v-model="searchKeyword" placeholder="搜索地区" />
    <el-button @click="searchRegions">搜索</el-button>

    <ul>
      <li v-for="result in searchResults" :key="result.value.join('-')">
        {{ result.fullPath }}
        <el-button @click="selectResult(result)">选择</el-button>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  data() {
    return {
      selectedValue: [],
      fullPath: "",
      lastName: "",
      searchKeyword: "",
      searchResults: []
    };
  },
  methods: {
    handleChange(value) {
      // 获取完整路径和最后一级名称
      this.fullPath = this.$refs.cascader.getFullNamePath(value);
      this.lastName = this.$refs.cascader.getLastLevelName(value);
    },

    searchRegions() {
      this.searchResults = this.$refs.cascader.searchByName(this.searchKeyword);
    },

    selectResult(result) {
      this.selectedValue = result.value;
    }
  }
};
</script>
```

## 数据格式

### 输入数据格式 (getGeographicalData 返回)

```javascript
{
  code: 0,
  data: {
    provinces: [
      {
        id: 1,
        code: "110000",
        name: "北京市"
      }
    ],
    cities: [
      {
        id: 1,
        code: "110100",
        name: "北京市",
        province_id: 1,
        province_code: "110000"
      }
    ],
    districts: [
      {
        id: 1,
        code: "110101",
        name: "东城区",
        city_id: 1,
        city_code: "110100"
      }
    ]
  }
}
```

### 输出数据格式

```javascript
// 选中值（v-model）
[110000, 110100, 110101][
  // 节点信息（node-change事件）
  ({
    value: 110000,
    label: "北京市",
    code: "110000",
    level: 1
  },
  {
    value: 110100,
    label: "北京市",
    code: "110100",
    level: 2
  },
  {
    value: 110101,
    label: "东城区",
    code: "110101",
    level: 3
  })
];
```

## 样式定制

组件使用 CSS 变量，支持主题切换：

```css
.geographical-cascader {
  /* 自定义样式 */
}

.code-text {
  color: var(--T3);
  font-size: 12px;
  margin-left: 4px;
}
```

## 注意事项

1. 组件依赖`getGeographicalData`接口，确保接口可用
2. 地理数据会自动缓存，提高性能
3. 支持响应式设计，适配移动端
4. 懒加载模式适合大数据量场景
5. 多选模式建议启用`collapseTags`避免标签过多
6. 非严格模式允许选择任意级别的节点

## 完整示例

参考 `GeographicalCascaderExample.vue` 文件查看完整的使用示例。
