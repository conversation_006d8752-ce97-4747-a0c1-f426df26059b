/**
 * 基础配置管理 API 测试文件
 * 用于测试和验证基础配置管理相关接口的功能
 */

import { 
  getResourceSiteRelations, 
  getSiteDeviceRelations, 
  clearBaseConfigCache,
  getCacheStatus 
} from './index.js';

/**
 * 测试资源-站点类型关联关系接口
 */
async function testResourceSiteRelations() {
  console.log('=== 测试资源-站点类型关联关系接口 ===');
  
  try {
    // 第一次调用（从服务器获取）
    console.log('第一次调用（从服务器获取）...');
    const startTime1 = Date.now();
    const result1 = await getResourceSiteRelations();
    const endTime1 = Date.now();
    console.log('响应时间:', endTime1 - startTime1, 'ms');
    console.log('返回结果:', result1);
    
    // 第二次调用（从缓存获取）
    console.log('\n第二次调用（从缓存获取）...');
    const startTime2 = Date.now();
    const result2 = await getResourceSiteRelations();
    const endTime2 = Date.now();
    console.log('响应时间:', endTime2 - startTime2, 'ms');
    console.log('缓存命中:', result1 === result2);
    
    // 强制刷新缓存
    console.log('\n强制刷新缓存...');
    const startTime3 = Date.now();
    const result3 = await getResourceSiteRelations(true);
    const endTime3 = Date.now();
    console.log('响应时间:', endTime3 - startTime3, 'ms');
    console.log('返回结果:', result3);
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

/**
 * 测试站点-设备类型关联关系接口
 */
async function testSiteDeviceRelations() {
  console.log('\n=== 测试站点-设备类型关联关系接口 ===');
  
  try {
    // 第一次调用
    console.log('第一次调用（从服务器获取）...');
    const startTime1 = Date.now();
    const result1 = await getSiteDeviceRelations();
    const endTime1 = Date.now();
    console.log('响应时间:', endTime1 - startTime1, 'ms');
    console.log('返回结果:', result1);
    
    // 第二次调用（从缓存获取）
    console.log('\n第二次调用（从缓存获取）...');
    const startTime2 = Date.now();
    const result2 = await getSiteDeviceRelations();
    const endTime2 = Date.now();
    console.log('响应时间:', endTime2 - startTime2, 'ms');
    console.log('缓存命中:', result1 === result2);
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

/**
 * 测试缓存管理功能
 */
function testCacheManagement() {
  console.log('\n=== 测试缓存管理功能 ===');
  
  // 查看缓存状态
  console.log('当前缓存状态:');
  console.log(getCacheStatus());
  
  // 清除特定缓存
  console.log('\n清除资源-站点关联关系缓存...');
  clearBaseConfigCache('resourceSiteRelations');
  console.log('清除后缓存状态:');
  console.log(getCacheStatus());
  
  // 清除所有缓存
  console.log('\n清除所有缓存...');
  clearBaseConfigCache();
  console.log('清除后缓存状态:');
  console.log(getCacheStatus());
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('开始运行基础配置管理 API 测试...\n');
  
  await testResourceSiteRelations();
  await testSiteDeviceRelations();
  testCacheManagement();
  
  console.log('\n测试完成！');
}

/**
 * 性能测试
 */
export async function performanceTest() {
  console.log('\n=== 性能测试 ===');
  
  // 清除缓存
  clearBaseConfigCache();
  
  // 测试并发请求
  console.log('测试并发请求...');
  const promises = [];
  for (let i = 0; i < 5; i++) {
    promises.push(getResourceSiteRelations());
  }
  
  const startTime = Date.now();
  const results = await Promise.all(promises);
  const endTime = Date.now();
  
  console.log('并发请求完成时间:', endTime - startTime, 'ms');
  console.log('所有请求返回相同结果:', results.every(r => r === results[0]));
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && window.location) {
  // 浏览器环境
  window.testBaseConfigAPI = {
    runAllTests,
    performanceTest,
    testResourceSiteRelations,
    testSiteDeviceRelations,
    testCacheManagement
  };
  console.log('基础配置管理 API 测试函数已挂载到 window.testBaseConfigAPI');
}
