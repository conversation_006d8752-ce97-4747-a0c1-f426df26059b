<template>
  <ElDrawer
    :title="$T('资源详情')"
    :visible.sync="openDrawer"
    destroy-on-close
    size="960px"
  >
    <div class="dialog-content">
      <div class="detail-grid">
        <!-- 第一行 -->
        <div class="detail-item">
          <div class="detail-label">{{ $T("电户号") }}</div>
          <div class="detail-value">
            {{ resourceDetail.electricityUserNumbers || "--" }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("资源名称") }}</div>
          <div class="detail-value">
            {{ resourceDetail.resourceName || "--" }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("站点数量") }}</div>
          <div class="detail-value">
            {{ resourceDetail.siteCount || "--" }}
          </div>
        </div>

        <!-- 第二行 -->
        <div class="detail-item">
          <div class="detail-label">{{ $T("技术容量") }} (kVA)</div>
          <div class="detail-value">
            {{ resourceDetail.registeredCapacity || "--" }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("平台直控") }}</div>
          <div class="detail-value">
            {{ resourceDetail.platformDirectControl ? "是" : "否" }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("资源类型") }}</div>
          <div class="detail-value">
            {{ getResourceTypeText(resourceDetail.resourceType) }}
          </div>
        </div>

        <!-- 第三行 -->
        <div class="detail-item">
          <div class="detail-label">{{ $T("响应方式") }}</div>
          <div class="detail-value">
            {{ getResponseModeText(resourceDetail.responseMode) }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("资源状态") }}</div>
          <div class="detail-value">
            {{ getResourceStatusText(resourceDetail.resourceStatus) }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("最大可运行功率") }} (kW)</div>
          <div class="detail-value">
            {{ resourceDetail.maximumOperablePower || "--" }}
          </div>
        </div>

        <!-- 第四行 -->
        <div class="detail-item">
          <div class="detail-label">{{ $T("最小可运行功率") }} (kW)</div>
          <div class="detail-value">
            {{ resourceDetail.minimumOperablePower || "--" }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("最大上调速率") }} (kW/min)</div>
          <div class="detail-value">
            {{ resourceDetail.maximumUpscalingRate || "--" }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("最大下调速率") }} (kW/min)</div>
          <div class="detail-value">
            {{ resourceDetail.maximumDownwardRate || "--" }}
          </div>
        </div>

        <!-- 第五行 -->
        <div class="detail-item">
          <div class="detail-label">{{ $T("经度") }}</div>
          <div class="detail-value">
            {{ resourceDetail.longitude || "--" }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("纬度") }}</div>
          <div class="detail-value">
            {{ resourceDetail.latitude || "--" }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("联系人") }}</div>
          <div class="detail-value">
            {{ resourceDetail.contactPerson || "--" }}
          </div>
        </div>

        <!-- 第六行 -->
        <div class="detail-item">
          <div class="detail-label">{{ $T("联系电话") }}</div>
          <div class="detail-value">
            {{ resourceDetail.phoneNumber || "--" }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">{{ $T("区域") }}</div>
          <div class="detail-value">
            {{
              getRegionText(
                resourceDetail.province,
                resourceDetail.city,
                resourceDetail.district
              )
            }}
          </div>
        </div>
        <div class="detail-item"></div>

        <!-- 第七行 - 地址占整行 -->
        <div class="detail-item detail-item-full">
          <div class="detail-label">{{ $T("地址") }}</div>
          <div class="detail-value">
            {{ resourceDetail.address || "--" }}
          </div>
        </div>
      </div>
    </div>
  </ElDrawer>
</template>

<script>
import { getGeographicalData } from "@/api/base-config";

export default {
  name: "ResourceDetailDrawer",
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      openDrawer: false,
      resourceDetail: {},
      geographicalData: null // 存储地理数据
    };
  },
  async mounted() {
    // 加载地理数据
    await this.loadGeographicalData();
  },
  watch: {
    visibleTrigger_in() {
      this.openDrawer = true;
      this.loadResourceDetail();
    },
    closeTrigger_in() {
      this.openDrawer = false;
    }
  },
  methods: {
    // 加载地理数据
    async loadGeographicalData() {
      try {
        const response = await getGeographicalData();
        if (response.code === 0) {
          this.geographicalData = response.data;
        } else {
          console.error("加载地理数据失败:", response.msg);
        }
      } catch (error) {
        console.error("加载地理数据失败:", error);
      }
    },
    loadResourceDetail() {
      // 直接使用API返回的原始数据，不进行字段重命名
      this.resourceDetail = this.inputData_in;
      console.log("ResourceDetailDrawer loaded data:", this.resourceDetail);
    },
    getResourceTypeText(type) {
      const typeMap = {
        1: "发电类",
        2: "储能类",
        3: "负荷类",
        4: "微电网",
        wind: "风电",
        solar: "光伏",
        storage: "储能",
        load: "负荷"
      };
      return typeMap[type] || type || "--";
    },
    getResponseModeText(mode) {
      const modeMap = {
        1: "自动",
        2: "手动",
        auto: "自动",
        manual: "手动"
      };
      return modeMap[mode] || mode || "--";
    },
    getResourceStatusText(status) {
      const statusMap = {
        1: "正常",
        2: "异常",
        3: "维护",
        4: "停用",
        normal: "正常",
        abnormal: "异常",
        maintenance: "维护",
        disabled: "停用"
      };
      return statusMap[status] || status || "--";
    },

    getRegionText(provinceId, cityId, districtId) {
      if (!this.geographicalData || !Array.isArray(this.geographicalData)) {
        return "--";
      }

      let regionText = "";

      // 查找省份名称
      if (provinceId) {
        const province = this.geographicalData.find(p => p.code === provinceId);
        if (province) {
          regionText = province.name;

          // 查找城市名称
          if (cityId && province.children && Array.isArray(province.children)) {
            const city = province.children.find(c => c.code === cityId);
            if (city && city.name !== province.name) {
              regionText += "/" + city.name;

              // 查找区县名称
              if (districtId && city.children && Array.isArray(city.children)) {
                const district = city.children.find(d => d.code === districtId);
                if (district && district.name !== city.name) {
                  regionText += "/" + district.name;
                }
              }
            }
          }
        }
      }

      return regionText || "--";
    }
  }
};
</script>

<style scoped>
/* 弹窗标题样式 */
:deep(.el-drawer__title) {
  color: var(--T1);
}

.dialog-content {
  padding: var(--J3);
  background: var(--BG1);
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--J3) var(--J2);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--J1);
}

.detail-item-full {
  grid-column: 1 / -1;
}

.detail-label {
  color: var(--T3);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: var(--J2);
}

.detail-value {
  color: var(--T2);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
  word-break: break-all;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .detail-grid {
    grid-template-columns: 1fr;
    gap: var(--J2);
  }

  .detail-item-full {
    grid-column: 1;
  }
}
</style>
