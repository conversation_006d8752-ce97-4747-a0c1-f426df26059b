# 虚拟电厂模块开发规范

## 项目结构规范

### 目录结构标准
```
src/
├── api/                          # API接口层
│   ├── vpp-resource.js          # 虚拟电厂资源接口
│   └── custom.js                # 自定义接口
├── components/                   # 公共组件
├── config/                      # 配置文件
│   └── lang/                    # 国际化配置
├── icons/                       # 图标资源
├── main.js                      # 入口文件
├── omega/                       # 插件注册
│   ├── index.js                 # 插件入口
│   ├── afterAppLogin.js         # 登录后处理
│   ├── http.js                  # HTTP配置
│   ├── i18n.js                  # 国际化配置
│   ├── subApp.js                # 子应用配置
│   └── theme.js                 # 主题配置
├── projects/                    # 业务项目
│   └── vpp-resource-manager/    # 虚拟电厂资源管理
├── resources/                   # 样式资源
├── router/                      # 路由配置
├── store/                       # 状态管理
│   ├── index.js                 # 主store
│   └── modules/                 # 模块化store
└── utils/                       # 工具函数
```

### 命名规范
- **文件名**：小驼峰命名法 (`vppResource.js`)
- **目录名**：短横线连接 (`vpp-resource-manager`)
- **组件名**：大驼峰命名法 (`VppManagement.vue`)
- **变量名**：小驼峰命名法 (`selectedNode`)
- **常量名**：大写+下划线 (`API_BASE_URL`)

## 编码规范

### Vue组件规范

#### 1. 组件结构顺序
```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
// 导入依赖
import { mapState, mapActions } from 'vuex'

export default {
  name: 'ComponentName',
  components: {},
  props: {},
  data() {
    return {}
  },
  computed: {
    ...mapState(['stateName'])
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    ...mapActions(['actionName'])
  }
}
</script>

<style lang="scss" scoped>
/* 组件样式 */
</style>
```

#### 2. Props定义规范
```javascript
props: {
  // 必填属性
  id: {
    type: [String, Number],
    required: true
  },
  // 可选属性
  title: {
    type: String,
    default: ''
  },
  // 复杂类型
  config: {
    type: Object,
    default: () => ({})
  },
  // 数组类型
  items: {
    type: Array,
    default: () => []
  }
}
```

#### 3. 事件命名规范
```javascript
// 组件内事件
methods: {
  handleClick() {
    // 向父组件发送事件，使用动词+名词格式
    this.$emit('update-status', newStatus)
    this.$emit('delete-item', itemId)
  }
}
```

### JavaScript规范

#### 1. ES6+语法使用
```javascript
// 使用const/let替代var
const apiUrl = '/api/v1'
let currentUser = null

// 使用箭头函数
const getUserList = () => {
  return fetch.get('/users')
}

// 使用模板字符串
const message = `欢迎 ${username} 登录系统`

// 使用解构赋值
const { name, age } = userInfo
const [first, second] = arrayData

// 使用扩展运算符
const newObj = { ...oldObj, newProp: 'value' }
```

#### 2. 异步处理规范
```javascript
// 使用async/await
async fetchData() {
  try {
    this.loading = true
    const response = await this.$store.dispatch('fetchUsers')
    this.users = response.data
  } catch (error) {
    this.$message.error('获取数据失败')
    console.error('API Error:', error)
  } finally {
    this.loading = false
  }
}
```

### CSS/SCSS规范

#### 1. 样式组织
```scss
// 使用SCSS变量
$primary-color: #1890ff;
$border-radius: 4px;

// 使用CSS变量（支持主题切换）
.component {
  color: var(--T1);
  background: var(--BG1);
  border-radius: var(--Ra);
}

// 使用Tailwind工具类
.container {
  @apply flex flex-col p-J4 bg-BG1 rounded-Ra;
}
```

#### 2. 响应式设计
```scss
// 移动端优先
.responsive-container {
  padding: 1rem;
  
  @media (min-width: 768px) {
    padding: 2rem;
  }
  
  @media (min-width: 1024px) {
    padding: 3rem;
  }
}
```

## API接口规范

### 1. API文件组织
```javascript
// src/api/vpp-resource.js
import fetch from "eem-base/utils/fetch"

const prefix = "/api/v1"

// 查询接口
export function listVpp(params) {
  return fetch({
    url: `${prefix}/vpp`,
    method: "GET",
    params
  })
}

// 创建接口
export function createVpp(data) {
  return fetch({
    url: `${prefix}/vpp`,
    method: "POST",
    data
  })
}

// 更新接口
export function updateVpp(id, data) {
  return fetch({
    url: `${prefix}/vpp/${id}`,
    method: "PUT",
    data
  })
}

// 删除接口
export function deleteVpp(id) {
  return fetch({
    url: `${prefix}/vpp/${id}`,
    method: "DELETE"
  })
}
```

### 2. 错误处理规范
```javascript
// 在组件中处理API错误
async handleSubmit() {
  try {
    await createVpp(this.formData)
    this.$message.success('创建成功')
    this.dialogVisible = false
    this.refreshList()
  } catch (error) {
    // 统一错误处理
    if (error.response?.status === 400) {
      this.$message.error('参数错误，请检查输入')
    } else if (error.response?.status === 401) {
      this.$message.error('无权限操作')
    } else {
      this.$message.error('操作失败，请重试')
    }
  }
}
```

## 状态管理规范

### 1. Vuex模块结构
```javascript
// src/store/modules/vppResource.js
const state = {
  vpps: [],
  users: [],
  resources: [],
  sites: [],
  devices: [],
  loading: false,
  error: null
}

const mutations = {
  SET_VPPS(state, vpps) {
    state.vpps = vpps
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  SET_ERROR(state, error) {
    state.error = error
  }
}

const actions = {
  async fetchVpps({ commit }, params) {
    commit('SET_LOADING', true)
    commit('SET_ERROR', null)
    
    try {
      const response = await listVpp(params)
      commit('SET_VPPS', response.data.records || [])
    } catch (error) {
      commit('SET_ERROR', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  }
}

const getters = {
  vppCount: state => state.vpps.length,
  activeVpps: state => state.vpps.filter(vpp => vpp.status === 'active')
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
```

### 2. 在组件中使用Store
```javascript
import { mapState, mapActions, mapGetters } from 'vuex'

export default {
  computed: {
    ...mapState('vppResource', ['vpps', 'loading']),
    ...mapGetters('vppResource', ['vppCount', 'activeVpps'])
  },
  methods: {
    ...mapActions('vppResource', ['fetchVpps']),
    
    async loadData() {
      await this.fetchVpps({ page: 1, size: 10 })
    }
  }
}
```

## 国际化规范

### 1. 国际化配置
```javascript
// src/config/lang/en.json
{
  "虚拟电厂": "Virtual Power Plant",
  "用户管理": "User Management",
  "资源管理": "Resource Management",
  "站点管理": "Site Management",
  "设备管理": "Device Management",
  "添加": "Add",
  "编辑": "Edit",
  "删除": "Delete",
  "确认": "Confirm",
  "取消": "Cancel"
}
```

### 2. 在组件中使用国际化
```vue
<template>
  <div>
    <h1>{{ $T('虚拟电厂') }}</h1>
    <el-button @click="handleAdd">{{ $T('添加') }}</el-button>
    <el-table :data="tableData">
      <el-table-column :label="$T('名称')" prop="name"></el-table-column>
      <el-table-column :label="$T('状态')" prop="status"></el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  methods: {
    handleAdd() {
      this.$message.success(this.$T('添加成功'))
    }
  }
}
</script>
```

## 组件开发规范

### 1. CET组件使用规范

#### CetTable使用
```vue
<template>
  <CetTable
    :data.sync="tableData"
    :dynamicInput.sync="dynamicInput"
    queryMode="trigger"
    dataMode="component"
    :dataConfig="dataConfig"
    :showPagination="true"
    :border="true"
    :highlightCurrentRow="true"
    @outputData_out="handleDataOutput"
    @currentChange_out="handleCurrentChange"
  >
    <el-table-column prop="name" :label="$T('名称')"></el-table-column>
    <el-table-column prop="status" :label="$T('状态')"></el-table-column>
    <el-table-column :label="$T('操作')" width="200">
      <template slot-scope="scope">
        <CetButton
          title="编辑"
          size="small"
          @statusTrigger_out="handleEdit(scope.row)"
        />
        <CetButton
          title="删除"
          size="small"
          type="danger"
          @statusTrigger_out="handleDelete(scope.row)"
        />
      </template>
    </el-table-column>
  </CetTable>
</template>

<script>
export default {
  data() {
    return {
      tableData: [],
      dynamicInput: {},
      dataConfig: {
        api: this.fetchData,
        immediate: true
      }
    }
  },
  methods: {
    async fetchData(params) {
      const response = await this.$store.dispatch('vppResource/fetchVpps', params)
      return response.data
    },
    handleDataOutput(data) {
      this.tableData = data.records || []
    },
    handleCurrentChange(row) {
      this.selectedRow = row
    },
    handleEdit(row) {
      // 编辑逻辑
    },
    handleDelete(row) {
      // 删除逻辑
    }
  }
}
</script>
```

#### CetButton使用
```vue
<template>
  <div class="button-group">
    <CetButton
      :visible_in="true"
      :disable_in="loading"
      title="添加虚拟电厂"
      size="small"
      type="primary"
      icon="el-icon-plus"
      @statusTrigger_out="handleAdd"
    />
    <CetButton
      :visible_in="selectedRows.length > 0"
      :disable_in="loading"
      title="批量删除"
      size="small"
      type="danger"
      @statusTrigger_out="handleBatchDelete"
    />
  </div>
</template>
```

#### CetTree使用
```vue
<template>
  <CetTree
    :inputData_in="treeData"
    :props="treeProps"
    :highlight-current="true"
    node-key="id"
    :default-expand-all="true"
    @node-click="handleNodeClick"
    :render-content="renderNode"
  />
</template>

<script>
export default {
  data() {
    return {
      treeData: [],
      treeProps: {
        label: 'name',
        children: 'children'
      }
    }
  },
  methods: {
    handleNodeClick(data, node) {
      this.$emit('select', data)
    },
    renderNode(h, { node, data }) {
      return (
        <span class="tree-node">
          <span>{data.name}</span>
          <span class="tree-node-count">({data.count})</span>
        </span>
      )
    }
  }
}
</script>
```

### 2. 表单组件规范
```vue
<template>
  <el-form
    ref="form"
    :model="formData"
    :rules="rules"
    label-width="100px"
  >
    <el-form-item :label="$T('名称')" prop="name">
      <el-input
        v-model="formData.name"
        :placeholder="$T('请输入名称')"
        clearable
      />
    </el-form-item>
    
    <el-form-item :label="$T('类型')" prop="type">
      <CetSimpleSelect
        v-model="formData.type"
        :option="{ key: 'value', label: 'label', value: 'value' }"
        :interface="{
          data: typeOptions,
          queryMode: 'trigger',
          dataMode: 'static'
        }"
        :placeholder="$T('请选择类型')"
      />
    </el-form-item>
    
    <el-form-item>
      <CetButton
        title="保存"
        type="primary"
        @statusTrigger_out="handleSubmit"
      />
      <CetButton
        title="取消"
        @statusTrigger_out="handleCancel"
      />
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        name: '',
        type: ''
      },
      rules: {
        name: [
          { required: true, message: this.$T('请输入名称'), trigger: 'blur' }
        ],
        type: [
          { required: true, message: this.$T('请选择类型'), trigger: 'change' }
        ]
      },
      typeOptions: [
        { value: 'type1', label: this.$T('类型1') },
        { value: 'type2', label: this.$T('类型2') }
      ]
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            await this.saveData(this.formData)
            this.$message.success(this.$T('保存成功'))
            this.$emit('success')
          } catch (error) {
            this.$message.error(this.$T('保存失败'))
          }
        }
      })
    },
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>
```

## 测试规范

### 1. 单元测试
```javascript
// tests/unit/components/VppManagement.spec.js
import { shallowMount } from '@vue/test-utils'
import VppManagement from '@/projects/vpp-resource-manager/resource-config/components/VppManagement.vue'

describe('VppManagement', () => {
  it('renders properly', () => {
    const wrapper = shallowMount(VppManagement)
    expect(wrapper.text()).toContain('虚拟电厂管理')
  })
  
  it('handles add button click', async () => {
    const wrapper = shallowMount(VppManagement)
    await wrapper.find('.add-btn').trigger('click')
    expect(wrapper.vm.dialogVisible).toBe(true)
  })
})
```

### 2. API测试
```javascript
// tests/unit/api/vpp-resource.spec.js
import { listVpp, createVpp } from '@/api/vpp-resource'

describe('VPP Resource API', () => {
  it('should fetch VPP list', async () => {
    const params = { page: 1, size: 10 }
    const response = await listVpp(params)
    expect(response.data).toHaveProperty('records')
  })
  
  it('should create VPP', async () => {
    const data = { name: 'Test VPP', type: 'type1' }
    const response = await createVpp(data)
    expect(response.data).toHaveProperty('id')
  })
})
```

## 性能优化规范

### 1. 组件懒加载
```javascript
// router/index.js
export default [
  {
    path: '/vpp-resource',
    name: 'VppResource',
    component: () => import('@/projects/vpp-resource-manager/resource-config/index.vue')
  }
]
```

### 2. 列表优化
```vue
<template>
  <!-- 使用虚拟滚动处理大数据量 -->
  <el-table
    v-if="tableData.length < 1000"
    :data="tableData"
  >
    <!-- 普通表格 -->
  </el-table>
  
  <!-- 大数据量使用虚拟滚动 -->
  <virtual-table
    v-else
    :data="tableData"
    :item-height="50"
    :visible-count="20"
  />
</template>
```

### 3. 图片优化
```vue
<template>
  <!-- 使用懒加载 -->
  <el-image
    :src="imageUrl"
    lazy
    :preview-src-list="[imageUrl]"
    fit="cover"
  />
</template>
```

## 代码提交规范

### 1. Git提交格式
```
type(scope): description

[optional body]

[optional footer]
```

### 2. 提交类型
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 重构代码
- **test**: 测试相关
- **chore**: 构建工具、依赖更新

### 3. 提交示例
```bash
feat(vpp-resource): add VPP management functionality

- Add VPP CRUD operations
- Implement VPP list with pagination
- Add VPP form validation

Closes #123
```

## 开发流程

### 1. 功能开发流程
1. 创建功能分支: `git checkout -b feature/vpp-management`
2. 开发功能模块
3. 编写单元测试
4. 自测功能完整性
5. 提交代码并推送
6. 创建合并请求
7. 代码审查
8. 合并到主分支

### 2. 代码审查检查点
- [ ] 代码符合编码规范
- [ ] 国际化文本已正确处理
- [ ] 组件props和events已正确定义
- [ ] 错误处理已实现
- [ ] 性能优化已考虑
- [ ] 测试用例已编写
- [ ] 文档已更新

### 3. 发布流程
1. 功能测试完成
2. 打包构建: `npm run build`
3. 部署到测试环境
4. 集成测试
5. 部署到生产环境
6. 发布说明文档

## 常见问题解决

### 1. 样式冲突
```scss
// 使用scoped样式
<style lang="scss" scoped>
.component {
  // 组件样式
}
</style>

// 深度选择器
<style lang="scss" scoped>
.component {
  ::v-deep .el-table {
    // 修改子组件样式
  }
}
</style>
```

### 2. 组件通信
```javascript
// 父子组件通信
// 父组件
<ChildComponent :data="parentData" @update="handleUpdate" />

// 子组件
this.$emit('update', newData)

// 兄弟组件通信
// 使用事件总线
this.$bus.$emit('event-name', data)
this.$bus.$on('event-name', handler)

// 或使用Vuex
this.$store.commit('updateData', data)
```

### 3. 内存泄漏防范
```javascript
// 组件销毁时清理
beforeDestroy() {
  // 清除定时器
  if (this.timer) {
    clearInterval(this.timer)
  }
  
  // 移除事件监听
  this.$bus.$off('event-name', this.handler)
  
  // 清空引用
  this.largeDataSet = null
}
```

遵循以上规范，可以确保虚拟电厂模块的代码质量、可维护性和团队协作效率。