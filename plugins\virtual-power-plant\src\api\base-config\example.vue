<template>
  <div class="base-config-example">
    <div class="header">
      <h2>基础配置管理示例</h2>
      <div class="actions">
        <el-button @click="loadData" :loading="loading">加载数据</el-button>
        <el-button @click="refreshData" :loading="loading">刷新缓存</el-button>
        <el-button @click="clearCache">清除缓存</el-button>
        <el-button @click="showCacheStatus">缓存状态</el-button>
      </div>
    </div>

    <div class="content">
      <!-- 资源-站点类型关联关系 -->
      <div class="section">
        <h3>资源-站点类型关联关系</h3>
        <div class="cache-info" v-if="cacheStatus.resourceSiteRelations">
          <span class="cache-badge" :class="getCacheBadgeClass('resourceSiteRelations')">
            {{ getCacheStatusText('resourceSiteRelations') }}
          </span>
          <span class="cache-time" v-if="cacheStatus.resourceSiteRelations.timestamp">
            缓存时间: {{ formatTime(cacheStatus.resourceSiteRelations.timestamp) }}
          </span>
        </div>
        <el-table 
          :data="resourceSiteRelations" 
          border 
          stripe
          v-loading="loading"
          empty-text="暂无数据">
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="resource_type_id" label="资源类型ID" width="120"></el-table-column>
          <el-table-column prop="site_type_id" label="站点类型ID" width="120"></el-table-column>
        </el-table>
      </div>

      <!-- 站点-设备类型关联关系 -->
      <div class="section">
        <h3>站点-设备类型关联关系</h3>
        <div class="cache-info" v-if="cacheStatus.siteDeviceRelations">
          <span class="cache-badge" :class="getCacheBadgeClass('siteDeviceRelations')">
            {{ getCacheStatusText('siteDeviceRelations') }}
          </span>
          <span class="cache-time" v-if="cacheStatus.siteDeviceRelations.timestamp">
            缓存时间: {{ formatTime(cacheStatus.siteDeviceRelations.timestamp) }}
          </span>
        </div>
        <el-table 
          :data="siteDeviceRelations" 
          border 
          stripe
          v-loading="loading"
          empty-text="暂无数据">
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="device_type_id" label="设备类型ID" width="120"></el-table-column>
          <el-table-column prop="site_iype_id" label="站点类型ID" width="120"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { 
  getResourceSiteRelations, 
  getSiteDeviceRelations, 
  clearBaseConfigCache,
  getCacheStatus 
} from './index.js';

export default {
  name: 'BaseConfigExample',
  data() {
    return {
      loading: false,
      resourceSiteRelations: [],
      siteDeviceRelations: [],
      cacheStatus: {}
    };
  },
  mounted() {
    this.loadData();
    this.updateCacheStatus();
  },
  methods: {
    /**
     * 加载数据
     */
    async loadData() {
      this.loading = true;
      try {
        // 并行加载两个接口的数据
        const [resourceSiteResult, siteDeviceResult] = await Promise.all([
          getResourceSiteRelations(),
          getSiteDeviceRelations()
        ]);

        if (resourceSiteResult.code === 0) {
          this.resourceSiteRelations = resourceSiteResult.data || [];
        } else {
          this.$message.error(`加载资源-站点关联关系失败: ${resourceSiteResult.msg}`);
        }

        if (siteDeviceResult.code === 0) {
          this.siteDeviceRelations = siteDeviceResult.data || [];
        } else {
          this.$message.error(`加载站点-设备关联关系失败: ${siteDeviceResult.msg}`);
        }

        this.updateCacheStatus();
        this.$message.success('数据加载完成');
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },

    /**
     * 刷新数据（强制刷新缓存）
     */
    async refreshData() {
      this.loading = true;
      try {
        const [resourceSiteResult, siteDeviceResult] = await Promise.all([
          getResourceSiteRelations(true),
          getSiteDeviceRelations(true)
        ]);

        if (resourceSiteResult.code === 0) {
          this.resourceSiteRelations = resourceSiteResult.data || [];
        }

        if (siteDeviceResult.code === 0) {
          this.siteDeviceRelations = siteDeviceResult.data || [];
        }

        this.updateCacheStatus();
        this.$message.success('缓存已刷新');
      } catch (error) {
        console.error('刷新数据失败:', error);
        this.$message.error('刷新数据失败');
      } finally {
        this.loading = false;
      }
    },

    /**
     * 清除缓存
     */
    clearCache() {
      clearBaseConfigCache();
      this.updateCacheStatus();
      this.$message.success('缓存已清除');
    },

    /**
     * 显示缓存状态
     */
    showCacheStatus() {
      this.updateCacheStatus();
      const status = JSON.stringify(this.cacheStatus, null, 2);
      this.$alert(status, '缓存状态', {
        confirmButtonText: '确定'
      });
    },

    /**
     * 更新缓存状态
     */
    updateCacheStatus() {
      this.cacheStatus = getCacheStatus();
    },

    /**
     * 获取缓存状态文本
     */
    getCacheStatusText(type) {
      const status = this.cacheStatus[type];
      if (!status) return '未知';
      if (!status.cached) return '未缓存';
      if (status.expired) return '已过期';
      return '已缓存';
    },

    /**
     * 获取缓存状态样式类
     */
    getCacheBadgeClass(type) {
      const status = this.cacheStatus[type];
      if (!status || !status.cached) return 'cache-none';
      if (status.expired) return 'cache-expired';
      return 'cache-valid';
    },

    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      if (!timestamp) return '';
      return new Date(timestamp).toLocaleString();
    }
  }
};
</script>

<style scoped>
.base-config-example {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.actions {
  display: flex;
  gap: 10px;
}

.section {
  margin-bottom: 30px;
}

.section h3 {
  margin-bottom: 10px;
  color: #333;
}

.cache-info {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.cache-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.cache-none {
  background-color: #f5f5f5;
  color: #999;
}

.cache-valid {
  background-color: #e7f5e7;
  color: #52c41a;
}

.cache-expired {
  background-color: #fff2e8;
  color: #fa8c16;
}

.cache-time {
  font-size: 12px;
  color: #666;
}
</style>
