<template>
  <el-image
    :src="src"
    :alt="alt"
    ref="img"
    v-bind="$attrs"
    :preview-src-list="srcList"
  >
    <div slot="error" class="image-slot">
      <i class="el-icon-picture-outline" />
    </div>
  </el-image>
</template>
<script>
import { HttpBase } from "@omega/http";

// 带 header 的IMG组件
export default {
  name: "FileImage",
  data() {
    return {
      src: "",
      srcList: [],
      alt: ""
    };
  },
  props: {
    source: String,
    onHandleImgLoaded: {
      type: Function,
      default(data) {
        return data.data;
      }
    },
    isHandleImgSrc: {
      type: Boolean,
      default: true
    },
    onHandleImgSrc: {
      type: Function
    },
    placeholderImg: {
      type: String
    },
    hideNotice: {
      type: Boolean,
      default: false
    },
    hideLoading: {
      type: Boolean,
      default: true
    },
    showPreview: {
      type: <PERSON>olean,
      default: false
    }
  },
  watch: {
    source(val) {
      this.update();
    }
  },
  mounted() {
    this.http = new HttpBase({
      loading: !this.hideLoading,
      json: false,
      silent: this.hideNotice
    });
    this.$nextTick(() => {
      this.update();
    });
  },
  methods: {
    update() {
      if (this.src) {
        // 释放之前的图片占用的内存
        window.URL.revokeObjectURL(this.src);
      }

      let source = this.$props && this.$props.source;
      if (source) {
        if (this.isHandleImgSrc) {
          source = this.onHandleImgSrc(source);
          this.alt = source;
          this.http({
            url: source,
            method: "GET",
            responseType: "blob"
          })
            .then(res => {
              if (!res) {
                this.$message.warning($T("文件不存在"));
                this.src = this.placeholderImg;
              } else {
                this.src = window.URL.createObjectURL(
                  this.onHandleImgLoaded(res)
                );
                this.srcList = this.showPreview ? [this.src] : [];
              }
            })
            .catch(err => {
              this.src = this.placeholderImg;
            });
        } else {
          this.src = source;
          this.srcList = this.showPreview ? [this.src] : [];
        }
      } else {
        this.src = this.placeholderImg;
      }
    }
  },
  beforeDestroy() {
    window.URL.revokeObjectURL(this.src);
  }
};
</script>
