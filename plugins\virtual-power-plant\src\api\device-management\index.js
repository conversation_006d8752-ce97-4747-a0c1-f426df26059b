import { httping } from "@omega/http";

/**
 * 分页查询设备列表
 * @param {Object} params - 查询参数
 * @param {number} [params.pageNum] - 页码（从1开始）
 * @param {number} [params.pageSize] - 每页数量
 * @param {string} [params.deviceId] - 设备编号
 * @param {string} [params.deviceName] - 设备名称
 * @param {string} [params.deviceType] - 设备类型
 * @param {string} [params.deviceStatus] - 设备状态
 * @param {string} [params.manufacturer] - 制造商
 * @param {number} [params.siteId] - 所属站点ID
 * @param {string} [params.orderBy] - 排序字段
 * @param {string} [params.orderDirection] - 排序方向
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: {
 *     records: Array<VppDeviceBO>, // 设备列表(实际返回驼峰命名)
 *     total: number, // 总记录数
 *     pageNum: number, // 当前页码
 *     pageSize: number, // 每页数量
 *     pages: number, // 总页数
 *     hasNext: boolean, // 是否有下一页
 *     hasPrevious: boolean // 是否有上一页
 *   },
 *   msg: string,
 *   total: number
 * }
 */
export function getDevicePage(params) {
  return httping({
    url: `/vpp/api/v1/resource-manager/device-config`,
    method: "GET",
    params
  });
}

/**
 * 创建设备
 * @param {Object} data - 设备数据对象(VppDeviceDTO格式-驼峰命名)
 * @param {string} data.deviceName - 电厂设备名称
 * @param {number} data.deviceType - 电厂设备类型ID
 * @param {number} data.siteId - 所属站点ID（必填）
 * @param {number} [data.ratedPower] - 额定功率
 * @param {Array} [data.monitorDeviceRelations] - 关联管网设备属性
 * @param {number} data.monitorDeviceRelations[].id - 管网设备ID
 * @param {string} data.monitorDeviceRelations[].name - 管网设备名称
 * @param {string} data.monitorDeviceRelations[].modelLabel - 管网设备类型标识
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: VppDeviceBO, // 设备详细信息(实际返回驼峰命名)
 *   msg: string,
 *   total: number
 * }
 */
export function createDevice(data) {
  return httping({
    url: `/vpp/api/v1/resource-manager/device-config`,
    method: "POST",
    data
  });
}

/**
 * 检查设备编号是否存在
 * @param {string} deviceId - 设备编号
 * @param {number} [excludeId] - 排除的设备ID（用于编辑时检查）
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: boolean, // true表示编号已存在，false表示不存在
 *   msg: string,
 *   total: number
 * }
 */
export function checkDeviceIdExists(deviceId, excludeId = null) {
  const params = { deviceId };
  if (excludeId) {
    params.excludeId = excludeId;
  }
  return httping({
    url: `/vpp/api/v1/resource-manager/device-config/check-id`,
    method: "GET",
    params
  });
}

/**
 * 根据ID查询设备详情
 * @param {number} id - 设备ID
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: VppDeviceDTO, // 设备详细信息
 *   msg: string,
 *   total: number
 * }
 */
export function getDeviceById(id) {
  return httping({
    url: `/vpp/api/v1/resource-manager/device-config/${id}`,
    method: "GET"
  });
}

/**
 * 更新设备
 * @param {number} id - 设备ID
 * @param {Object} data - 设备数据对象（同创建设备的参数）
 * @returns {Promise} 返回Promise对象
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: VppDeviceBO, // 更新后的设备信息
 *   msg: string,
 *   total: number
 * }
 */
export function updateDevice(id, data) {
  return httping({
    url: `/vpp/api/v1/resource-manager/device-config/${id}`,
    method: "PUT",
    data
  });
}

/**
 * 删除设备
 * @param {Object} deleteNode - 删除对象
 * @returns {Promise} 返回Promise对象
 * 入参对象
 * {
 *   parentId：number站点id,
 *   ids:array 电厂设备id集合
 * }
 * 返回数据结构：
 * {
 *   code: 0,
 *   data: boolean, // true表示删除成功
 *   msg: string,
 *   total: number
 * }
 */
export function deleteDevice(deleteNode) {
  return httping({
    url: `/vpp/api/v1/resource-manager/device-config/delete-by-ids`,
    method: "DELETE",
    data: deleteNode
  });
}

/**
 * 批量删除设备
 * @param {Object} deleteNode - 删除参数对象
 * @param {number} deleteNode.parentId - 站点ID
 * @param {Array<number>} deleteNode.ids - 设备ID数组
 * @returns {Promise} 返回Promise对象
 */
export function batchDeleteDevices(deleteNode) {
  return deleteDevice(deleteNode);
}
