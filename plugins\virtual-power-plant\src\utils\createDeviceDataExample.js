/**
 * 创建设备API数据结构示例
 * 用于验证和测试设备创建时的数据格式
 */

/**
 * 生成创建设备的示例数据
 * @param {Object} options - 配置选项
 * @param {string} options.deviceName - 设备名称
 * @param {number} options.deviceType - 设备类型ID
 * @param {number} options.siteId - 站点ID
 * @param {number} options.ratedPower - 额定功率
 * @param {Object} options.networkDevice - 管网设备信息
 * @returns {Object} 符合API要求的设备数据
 */
export function generateCreateDeviceData(options = {}) {
  const {
    deviceName = "测试设备",
    deviceType = 1, // 默认为储能设备
    siteId = 7,
    ratedPower = 0,
    networkDevice = null
  } = options;

  const deviceData = {
    deviceName,
    deviceType,
    siteId,
    ratedPower
  };

  // 如果有管网设备信息，添加到monitorDeviceRelations数组中
  if (networkDevice) {
    deviceData.monitorDeviceRelations = [
      {
        id: networkDevice.id,
        name: networkDevice.name,
        modelLabel: networkDevice.modelLabel
      }
    ];
  }

  return deviceData;
}

/**
 * 设备类型映射
 */
export const DEVICE_TYPE_MAP = {
  ENERGY_STORAGE: 1,    // 储能设备
  PHOTOVOLTAIC: 2,      // 光伏设备
  WIND_POWER: 3,        // 风电设备
  FAN_PUMP: 4,          // 风机泵类
  AIR_CONDITIONING: 5,  // 风冷式空调
  CHARGING: 6           // 充电设备
};

/**
 * 创建设备数据示例
 */
export const DEVICE_DATA_EXAMPLES = {
  // 示例1：不关联管网设备的储能设备
  storageDeviceOnly: {
    deviceName: "储能设备001",
    deviceType: DEVICE_TYPE_MAP.ENERGY_STORAGE,
    siteId: 7,
    ratedPower: 100
  },

  // 示例2：关联光伏逆变器的光伏设备
  photovoltaicWithInverter: {
    deviceName: "光伏设备001",
    deviceType: DEVICE_TYPE_MAP.PHOTOVOLTAIC,
    siteId: 7,
    ratedPower: 200,
    monitorDeviceRelations: [
      {
        id: 1,
        name: "光伏逆变器001",
        modelLabel: "pv_inverter"
      }
    ]
  },

  // 示例3：关联电池设备的储能设备
  storageWithBattery: {
    deviceName: "储能设备002",
    deviceType: DEVICE_TYPE_MAP.ENERGY_STORAGE,
    siteId: 7,
    ratedPower: 150,
    monitorDeviceRelations: [
      {
        id: 2,
        name: "电池组001",
        modelLabel: "battery"
      }
    ]
  },

  // 示例4：关联PCS设备的储能设备
  storageWithPCS: {
    deviceName: "储能设备003",
    deviceType: DEVICE_TYPE_MAP.ENERGY_STORAGE,
    siteId: 7,
    ratedPower: 300,
    monitorDeviceRelations: [
      {
        id: 3,
        name: "PCS设备001",
        modelLabel: "pcs"
      }
    ]
  },

  // 示例5：关联充电桩的充电设备
  chargingWithStation: {
    deviceName: "充电设备001",
    deviceType: DEVICE_TYPE_MAP.CHARGING,
    siteId: 7,
    ratedPower: 50,
    monitorDeviceRelations: [
      {
        id: 4,
        name: "充电桩001",
        modelLabel: "pv_chargingstation"
      }
    ]
  }
};

/**
 * 验证设备数据格式是否正确
 * @param {Object} deviceData - 设备数据
 * @returns {Object} 验证结果
 */
export function validateDeviceData(deviceData) {
  const errors = [];
  const warnings = [];

  // 必填字段检查
  if (!deviceData.deviceName || typeof deviceData.deviceName !== 'string') {
    errors.push('deviceName 是必填字段，且必须是字符串');
  }

  if (!deviceData.deviceType || typeof deviceData.deviceType !== 'number') {
    errors.push('deviceType 是必填字段，且必须是数字');
  }

  if (!deviceData.siteId || typeof deviceData.siteId !== 'number') {
    errors.push('siteId 是必填字段，且必须是数字');
  }

  // 可选字段检查
  if (deviceData.ratedPower !== undefined && typeof deviceData.ratedPower !== 'number') {
    warnings.push('ratedPower 应该是数字类型');
  }

  // 管网设备关联检查
  if (deviceData.monitorDeviceRelations) {
    if (!Array.isArray(deviceData.monitorDeviceRelations)) {
      errors.push('monitorDeviceRelations 必须是数组');
    } else {
      deviceData.monitorDeviceRelations.forEach((relation, index) => {
        if (!relation.id || typeof relation.id !== 'number') {
          errors.push(`monitorDeviceRelations[${index}].id 是必填字段，且必须是数字`);
        }
        if (!relation.name || typeof relation.name !== 'string') {
          errors.push(`monitorDeviceRelations[${index}].name 是必填字段，且必须是字符串`);
        }
        if (!relation.modelLabel || typeof relation.modelLabel !== 'string') {
          errors.push(`monitorDeviceRelations[${index}].modelLabel 是必填字段，且必须是字符串`);
        }
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 打印设备数据示例
 */
export function printExamples() {
  console.log('🔧 设备创建API数据结构示例:\n');
  
  Object.entries(DEVICE_DATA_EXAMPLES).forEach(([key, example]) => {
    console.log(`📋 ${key}:`);
    console.log(JSON.stringify(example, null, 2));
    console.log('');
  });
}

// 如果直接运行此文件，打印示例
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.printDeviceExamples = printExamples;
} else if (typeof module !== 'undefined' && module.exports) {
  // Node.js 环境
  printExamples();
}
