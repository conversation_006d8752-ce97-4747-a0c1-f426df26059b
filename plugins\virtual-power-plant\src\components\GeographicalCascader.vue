<template>
  <div class="geographical-cascader">
    <ElCascader
      v-model="selectedValue"
      :options="cascaderOptions"
      :props="cascaderProps"
      :placeholder="placeholder"
      :clearable="clearable"
      :disabled="disabled"
      :size="size"
      :filterable="filterable"
      :show-all-levels="showAllLevels"
      :collapse-tags="collapseTags"
      @change="handleChange"
      @expand-change="handleExpandChange"
      @blur="handleBlur"
      @focus="handleFocus"
      @visible-change="handleVisibleChange"
      @remove-tag="handleRemoveTag"
    >
      <template #default="{ node, data }">
        <span>{{ data.label }}</span>
        <span v-if="showCode" class="code-text">({{ data.code }})</span>
      </template>
    </ElCascader>
  </div>
</template>

<script>
import { getGeographicalData } from "@/api/base-config";

export default {
  name: "GeographicalCascader",
  props: {
    // v-model 绑定的值
    value: {
      type: Array,
      default: () => []
    },
    // 占位符
    placeholder: {
      type: String,
      default: "请选择省份/城市/区县"
    },
    // 是否可清空
    clearable: {
      type: Boolean,
      default: true
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 尺寸
    size: {
      type: String,
      default: "medium",
      validator: value => ["large", "medium", "small", "mini"].includes(value)
    },
    // 是否可搜索
    filterable: {
      type: Boolean,
      default: true
    },
    // 是否显示完整路径
    showAllLevels: {
      type: Boolean,
      default: true
    },
    // 多选时是否折叠标签
    collapseTags: {
      type: Boolean,
      default: false
    },
    // 是否显示编码
    showCode: {
      type: Boolean,
      default: false
    },
    // 级联层级：1-省份，2-城市，3-区县
    level: {
      type: Number,
      default: 3,
      validator: value => [1, 2, 3].includes(value)
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否严格模式（只能选择叶子节点）
    checkStrictly: {
      type: Boolean,
      default: false
    },
    // 是否懒加载
    lazy: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 级联选择器的选项数据
      cascaderOptions: [],
      // 当前选中的值
      selectedValue: [],
      // 加载状态
      loading: false,
      // 原始地理数据
      geographicalData: null
    };
  },
  computed: {
    // 级联选择器配置
    cascaderProps() {
      return {
        value: "value",
        label: "label",
        children: "children",
        disabled: "disabled",
        leaf: "leaf",
        multiple: this.multiple,
        checkStrictly: this.checkStrictly,
        lazy: this.lazy,
        lazyLoad: this.lazy ? this.lazyLoad : undefined
      };
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.selectedValue = newVal || [];
      },
      immediate: true
    },
    selectedValue(newVal) {
      this.$emit("input", newVal);
    },
    level() {
      // 当级联层级改变时，重新构建选项数据
      this.buildCascaderOptions();
    }
  },
  async mounted() {
    await this.loadGeographicalData();
  },
  methods: {
    /**
     * 加载地理数据
     */
    async loadGeographicalData() {
      try {
        this.loading = true;
        const response = await getGeographicalData();

        if (response.code === 0) {
          this.geographicalData = response.data;
          this.buildCascaderOptions();
        } else {
          this.$message.error(response.msg || "加载地理数据失败");
        }
      } catch (error) {
        console.error("加载地理数据失败:", error);
        this.$message.error("加载地理数据失败");
      } finally {
        this.loading = false;
      }
    },

    /**
     * 构建级联选择器选项数据
     */
    buildCascaderOptions() {
      if (!this.geographicalData) return;

      const { provinces, cities, districts } = this.geographicalData;

      // 构建省份选项
      const provinceOptions = provinces.map(province => {
        const provinceOption = {
          value: province.id || province.code,
          label: province.name,
          code: province.code,
          children: []
        };

        // 如果只需要省份级别，设置为叶子节点
        if (this.level === 1) {
          provinceOption.leaf = true;
          delete provinceOption.children;
          return provinceOption;
        }

        // 构建城市选项
        const provinceCities = cities.filter(
          city =>
            city.province_id === province.id ||
            city.province_code === province.code
        );

        provinceOption.children = provinceCities.map(city => {
          const cityOption = {
            value: city.id || city.code,
            label: city.name,
            code: city.code,
            children: []
          };

          // 如果只需要城市级别，设置为叶子节点
          if (this.level === 2) {
            cityOption.leaf = true;
            delete cityOption.children;
            return cityOption;
          }

          // 构建区县选项
          const cityDistricts = districts.filter(
            district =>
              district.city_id === city.id || district.city_code === city.code
          );

          cityOption.children = cityDistricts.map(district => ({
            value: district.id || district.code,
            label: district.name,
            code: district.code,
            leaf: true
          }));

          // 如果没有区县数据，设置城市为叶子节点
          if (cityOption.children.length === 0) {
            cityOption.leaf = true;
            delete cityOption.children;
          }

          return cityOption;
        });

        // 如果没有城市数据，设置省份为叶子节点
        if (provinceOption.children.length === 0) {
          provinceOption.leaf = true;
          delete provinceOption.children;
        }

        return provinceOption;
      });

      this.cascaderOptions = provinceOptions;
    },

    /**
     * 懒加载方法（如果启用懒加载）
     */
    lazyLoad(node, resolve) {
      const { level, value } = node;

      if (!this.geographicalData) {
        resolve([]);
        return;
      }

      const { provinces, cities, districts } = this.geographicalData;
      let options = [];

      if (level === 0) {
        // 加载省份
        options = provinces.map(province => ({
          value: province.id || province.code,
          label: province.name,
          code: province.code,
          leaf: this.level === 1
        }));
      } else if (level === 1) {
        // 加载城市
        const provinceCities = cities.filter(
          city => city.province_id === value || city.province_code === value
        );
        options = provinceCities.map(city => ({
          value: city.id || city.code,
          label: city.name,
          code: city.code,
          leaf: this.level === 2
        }));
      } else if (level === 2) {
        // 加载区县
        const cityDistricts = districts.filter(
          district => district.city_id === value || district.city_code === value
        );
        options = cityDistricts.map(district => ({
          value: district.id || district.code,
          label: district.name,
          code: district.code,
          leaf: true
        }));
      }

      resolve(options);
    },

    /**
     * 选择改变事件
     */
    handleChange(value) {
      this.$emit("change", value);

      // 获取选中的节点信息
      const selectedNodes = this.getSelectedNodes(value);
      this.$emit("node-change", selectedNodes);
    },

    /**
     * 展开改变事件
     */
    handleExpandChange(value) {
      this.$emit("expand-change", value);
    },

    /**
     * 失焦事件
     */
    handleBlur(event) {
      this.$emit("blur", event);
    },

    /**
     * 聚焦事件
     */
    handleFocus(event) {
      this.$emit("focus", event);
    },

    /**
     * 下拉框显示/隐藏事件
     */
    handleVisibleChange(visible) {
      this.$emit("visible-change", visible);
    },

    /**
     * 多选时移除标签事件
     */
    handleRemoveTag(value) {
      this.$emit("remove-tag", value);
    },

    /**
     * 获取选中的节点信息
     */
    getSelectedNodes(value) {
      if (!value || value.length === 0) return [];

      const nodes = [];
      let currentOptions = this.cascaderOptions;

      for (let i = 0; i < value.length; i++) {
        const nodeValue = value[i];
        const node = currentOptions.find(option => option.value === nodeValue);

        if (node) {
          nodes.push({
            value: node.value,
            label: node.label,
            code: node.code,
            level: i + 1
          });
          currentOptions = node.children || [];
        }
      }

      return nodes;
    },

    /**
     * 根据编码获取完整路径
     */
    getPathByCode(provinceCode, cityCode = null, districtCode = null) {
      const path = [];

      if (!this.geographicalData) return path;

      const { provinces, cities, districts } = this.geographicalData;

      // 查找省份
      const province = provinces.find(p => p.code === provinceCode);
      if (province) {
        path.push(province.id || province.code);

        if (cityCode) {
          // 查找城市
          const city = cities.find(
            c =>
              c.code === cityCode &&
              (c.province_id === province.id ||
                c.province_code === province.code)
          );
          if (city) {
            path.push(city.id || city.code);

            if (districtCode) {
              // 查找区县
              const district = districts.find(
                d =>
                  d.code === districtCode &&
                  (d.city_id === city.id || d.city_code === city.code)
              );
              if (district) {
                path.push(district.id || district.code);
              }
            }
          }
        }
      }

      return path;
    },

    /**
     * 刷新地理数据
     */
    async refresh() {
      await this.loadGeographicalData();
    },

    /**
     * 根据值获取完整的地区名称路径
     */
    getFullNamePath(value) {
      if (!value || value.length === 0) return "";

      const nodes = this.getSelectedNodes(value);
      return nodes.map(node => node.label).join(" / ");
    },

    /**
     * 根据值获取最后一级的地区名称
     */
    getLastLevelName(value) {
      if (!value || value.length === 0) return "";

      const nodes = this.getSelectedNodes(value);
      return nodes.length > 0 ? nodes[nodes.length - 1].label : "";
    },

    /**
     * 验证选中的值是否有效
     */
    validateValue(value) {
      if (!value || !Array.isArray(value)) return false;

      // 检查每一级是否存在
      let currentOptions = this.cascaderOptions;
      for (let i = 0; i < value.length; i++) {
        const nodeValue = value[i];
        const node = currentOptions.find(option => option.value === nodeValue);

        if (!node) return false;

        currentOptions = node.children || [];
      }

      return true;
    },

    /**
     * 获取指定省份下的所有城市
     */
    getCitiesByProvince(provinceValue) {
      if (!this.geographicalData) return [];

      const { cities } = this.geographicalData;
      return cities.filter(
        city =>
          city.province_id === provinceValue ||
          city.province_code === provinceValue
      );
    },

    /**
     * 获取指定城市下的所有区县
     */
    getDistrictsByCity(cityValue) {
      if (!this.geographicalData) return [];

      const { districts } = this.geographicalData;
      return districts.filter(
        district =>
          district.city_id === cityValue || district.city_code === cityValue
      );
    },

    /**
     * 根据名称搜索地区
     */
    searchByName(keyword) {
      if (!this.geographicalData || !keyword) return [];

      const { provinces, cities, districts } = this.geographicalData;
      const results = [];

      // 搜索省份
      provinces.forEach(province => {
        if (province.name.includes(keyword)) {
          results.push({
            type: "province",
            value: [province.id || province.code],
            label: province.name,
            fullPath: province.name
          });
        }
      });

      // 搜索城市
      cities.forEach(city => {
        if (city.name.includes(keyword)) {
          const province = provinces.find(
            p => p.id === city.province_id || p.code === city.province_code
          );
          if (province) {
            results.push({
              type: "city",
              value: [province.id || province.code, city.id || city.code],
              label: city.name,
              fullPath: `${province.name} / ${city.name}`
            });
          }
        }
      });

      // 搜索区县
      districts.forEach(district => {
        if (district.name.includes(keyword)) {
          const city = cities.find(
            c => c.id === district.city_id || c.code === district.city_code
          );
          if (city) {
            const province = provinces.find(
              p => p.id === city.province_id || p.code === city.province_code
            );
            if (province) {
              results.push({
                type: "district",
                value: [
                  province.id || province.code,
                  city.id || city.code,
                  district.id || district.code
                ],
                label: district.name,
                fullPath: `${province.name} / ${city.name} / ${district.name}`
              });
            }
          }
        }
      });

      return results;
    }
  }
};
</script>

<style scoped>
.geographical-cascader {
  width: 100%;
}

.code-text {
  color: var(--T3);
  font-size: 12px;
  margin-left: 4px;
}
</style>
